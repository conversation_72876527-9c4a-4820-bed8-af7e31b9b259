#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AE多线程渲染系统 - 图形界面
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import time
import os
from pathlib import Path
from ae_render_manager import AERenderManager

class AERenderGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("AE多线程渲染系统")
        self.root.geometry("800x600")
        
        # 创建渲染管理器
        self.manager = AERenderManager()
        self.manager_thread = None
        self.is_manager_running = False
        
        # 创建界面
        self.create_widgets()
        
        # 启动状态更新线程
        self.update_thread = threading.Thread(target=self.update_status_loop, daemon=True)
        self.update_thread.start()
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(4, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="AE多线程渲染系统", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 配置区域
        config_frame = ttk.LabelFrame(main_frame, text="配置", padding="10")
        config_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        config_frame.columnconfigure(1, weight=1)
        
        # AE路径
        ttk.Label(config_frame, text="AE路径:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.ae_path_var = tk.StringVar(value=self.manager.config["ae_path"])
        ae_path_entry = ttk.Entry(config_frame, textvariable=self.ae_path_var, width=50)
        ae_path_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        ttk.Button(config_frame, text="浏览", command=self.browse_ae_path).grid(row=0, column=2)
        
        # 最大线程数
        ttk.Label(config_frame, text="最大线程数:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(10, 0))
        self.max_threads_var = tk.StringVar(value=str(self.manager.config["max_threads"]))
        threads_spinbox = ttk.Spinbox(config_frame, from_=1, to=16, textvariable=self.max_threads_var, width=10)
        threads_spinbox.grid(row=1, column=1, sticky=tk.W, pady=(10, 0))
        
        # 保存配置按钮
        ttk.Button(config_frame, text="保存配置", command=self.save_config).grid(row=1, column=2, pady=(10, 0))
        
        # 操作区域
        operation_frame = ttk.LabelFrame(main_frame, text="操作", padding="10")
        operation_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 按钮
        ttk.Button(operation_frame, text="添加AE项目", command=self.add_project).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(operation_frame, text="启动渲染管理器", command=self.start_manager).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(operation_frame, text="停止渲染管理器", command=self.stop_manager).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(operation_frame, text="清理完成记录", command=self.clear_completed).pack(side=tk.LEFT, padx=(0, 10))
        
        # 状态区域
        status_frame = ttk.LabelFrame(main_frame, text="状态", padding="10")
        status_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        status_frame.columnconfigure(1, weight=1)
        status_frame.columnconfigure(3, weight=1)
        status_frame.columnconfigure(5, weight=1)
        
        # 状态标签
        ttk.Label(status_frame, text="队列中:").grid(row=0, column=0, sticky=tk.W)
        self.queue_count_var = tk.StringVar(value="0")
        ttk.Label(status_frame, textvariable=self.queue_count_var, font=("Arial", 12, "bold")).grid(row=0, column=1, sticky=tk.W, padx=(5, 20))
        
        ttk.Label(status_frame, text="正在渲染:").grid(row=0, column=2, sticky=tk.W)
        self.running_count_var = tk.StringVar(value="0")
        ttk.Label(status_frame, textvariable=self.running_count_var, font=("Arial", 12, "bold")).grid(row=0, column=3, sticky=tk.W, padx=(5, 20))
        
        ttk.Label(status_frame, text="已完成:").grid(row=0, column=4, sticky=tk.W)
        self.completed_count_var = tk.StringVar(value="0")
        ttk.Label(status_frame, textvariable=self.completed_count_var, font=("Arial", 12, "bold")).grid(row=0, column=5, sticky=tk.W, padx=(5, 0))
        
        # 管理器状态
        ttk.Label(status_frame, text="管理器状态:").grid(row=1, column=0, sticky=tk.W, pady=(10, 0))
        self.manager_status_var = tk.StringVar(value="已停止")
        status_label = ttk.Label(status_frame, textvariable=self.manager_status_var, font=("Arial", 12, "bold"))
        status_label.grid(row=1, column=1, sticky=tk.W, padx=(5, 0), pady=(10, 0))
        
        # 详细信息区域
        detail_frame = ttk.LabelFrame(main_frame, text="详细信息", padding="10")
        detail_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        detail_frame.columnconfigure(0, weight=1)
        detail_frame.rowconfigure(0, weight=1)
        
        # 创建Notebook
        self.notebook = ttk.Notebook(detail_frame)
        self.notebook.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 队列标签页
        self.queue_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.queue_frame, text="队列")
        
        self.queue_text = scrolledtext.ScrolledText(self.queue_frame, height=10)
        self.queue_text.pack(fill=tk.BOTH, expand=True)
        
        # 运行中标签页
        self.running_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.running_frame, text="运行中")
        
        self.running_text = scrolledtext.ScrolledText(self.running_frame, height=10)
        self.running_text.pack(fill=tk.BOTH, expand=True)
        
        # 已完成标签页
        self.completed_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.completed_frame, text="已完成")
        
        self.completed_text = scrolledtext.ScrolledText(self.completed_frame, height=10)
        self.completed_text.pack(fill=tk.BOTH, expand=True)
        
        # 日志标签页
        self.log_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.log_frame, text="日志")
        
        self.log_text = scrolledtext.ScrolledText(self.log_frame, height=10)
        self.log_text.pack(fill=tk.BOTH, expand=True)
    
    def browse_ae_path(self):
        """浏览AE路径"""
        filename = filedialog.askopenfilename(
            title="选择AE渲染器",
            filetypes=[("可执行文件", "*.exe"), ("所有文件", "*.*")]
        )
        if filename:
            self.ae_path_var.set(filename)
    
    def save_config(self):
        """保存配置"""
        try:
            self.manager.config["ae_path"] = self.ae_path_var.get()
            self.manager.config["max_threads"] = int(self.max_threads_var.get())
            
            if self.manager.save_config():
                messagebox.showinfo("成功", "配置已保存")
                self.log("配置已保存")
            else:
                messagebox.showerror("错误", "配置保存失败")
        except ValueError:
            messagebox.showerror("错误", "线程数必须是数字")
    
    def add_project(self):
        """添加AE项目"""
        filenames = filedialog.askopenfilenames(
            title="选择AE项目文件",
            filetypes=[("AE项目文件", "*.aep"), ("所有文件", "*.*")]
        )
        
        for filename in filenames:
            success, message = self.manager.add_project_to_queue(filename)
            if success:
                self.log(f"✓ {message}")
            else:
                self.log(f"✗ {message}")
    
    def start_manager(self):
        """启动渲染管理器"""
        if not self.is_manager_running:
            self.manager_thread = threading.Thread(target=self.run_manager, daemon=True)
            self.manager_thread.start()
            self.is_manager_running = True
            self.log("渲染管理器已启动")
    
    def stop_manager(self):
        """停止渲染管理器"""
        if self.is_manager_running:
            self.manager.stop_manager()
            self.is_manager_running = False
            self.log("渲染管理器已停止")
    
    def run_manager(self):
        """运行渲染管理器"""
        try:
            self.manager.start_manager()
        except Exception as e:
            self.log(f"管理器运行错误: {e}")
        finally:
            self.is_manager_running = False
    
    def clear_completed(self):
        """清理完成记录"""
        if messagebox.askyesno("确认", "确定要清理所有完成记录吗？"):
            try:
                for file in self.manager.completed_dir.glob("*.json"):
                    file.unlink()
                self.log("已清理完成记录")
            except Exception as e:
                self.log(f"清理失败: {e}")
    
    def log(self, message):
        """添加日志"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
    
    def update_status_loop(self):
        """状态更新循环"""
        while True:
            try:
                self.update_status()
                time.sleep(2)
            except Exception as e:
                print(f"状态更新错误: {e}")
                time.sleep(5)
    
    def update_status(self):
        """更新状态显示"""
        status = self.manager.get_status()
        
        # 更新计数
        self.queue_count_var.set(str(status["queue_count"]))
        self.running_count_var.set(str(status["running_count"]))
        self.completed_count_var.set(str(status["completed_count"]))
        
        # 更新管理器状态
        if self.is_manager_running:
            self.manager_status_var.set("运行中")
        else:
            self.manager_status_var.set("已停止")
        
        # 更新详细信息
        self.update_queue_info()
        self.update_running_info()
        self.update_completed_info()
    
    def update_queue_info(self):
        """更新队列信息"""
        self.queue_text.delete(1.0, tk.END)
        
        for queue_file in self.manager.queue_dir.glob("*.json"):
            try:
                import json
                with open(queue_file, 'r', encoding='utf-8') as f:
                    info = json.load(f)
                
                self.queue_text.insert(tk.END, f"项目: {info['project_name']}\n")
                self.queue_text.insert(tk.END, f"路径: {info['project_path']}\n")
                self.queue_text.insert(tk.END, f"添加时间: {info['added_time']}\n")
                self.queue_text.insert(tk.END, "-" * 50 + "\n")
            except:
                pass
    
    def update_running_info(self):
        """更新运行信息"""
        self.running_text.delete(1.0, tk.END)
        
        for running_file in self.manager.running_dir.glob("*.json"):
            try:
                import json
                with open(running_file, 'r', encoding='utf-8') as f:
                    info = json.load(f)
                
                self.running_text.insert(tk.END, f"项目: {info['project_name']}\n")
                self.running_text.insert(tk.END, f"路径: {info['project_path']}\n")
                self.running_text.insert(tk.END, f"开始时间: {info['start_time']}\n")
                if 'pid' in info:
                    self.running_text.insert(tk.END, f"进程ID: {info['pid']}\n")
                self.running_text.insert(tk.END, "-" * 50 + "\n")
            except:
                pass
    
    def update_completed_info(self):
        """更新完成信息"""
        self.completed_text.delete(1.0, tk.END)
        
        completed_files = sorted(self.manager.completed_dir.glob("*.json"), 
                               key=lambda x: x.stat().st_mtime, reverse=True)
        
        for completed_file in completed_files[:10]:  # 只显示最近10个
            try:
                import json
                with open(completed_file, 'r', encoding='utf-8') as f:
                    info = json.load(f)
                
                self.completed_text.insert(tk.END, f"项目: {info['project_name']}\n")
                self.completed_text.insert(tk.END, f"完成时间: {info['end_time']}\n")
                self.completed_text.insert(tk.END, f"返回码: {info['return_code']}\n")
                self.completed_text.insert(tk.END, "-" * 50 + "\n")
            except:
                pass
    
    def run(self):
        """运行GUI"""
        self.root.mainloop()


if __name__ == "__main__":
    app = AERenderGUI()
    app.run()
