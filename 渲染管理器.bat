@echo off
setlocal enabledelayedexpansion
title 渲染管理器 - AE多线程渲染系统

:: 默认配置参数
set "DEFAULT_AE_PATH=D:\soft\AE2023\Adobe After Effects 2023\Support Files\aerender.exe"
set "DEFAULT_MAX_THREADS=4"

:: 读取配置文件
set "CONFIG_FILE=%~dp0config.ini"
set "AE_PATH=%DEFAULT_AE_PATH%"
set "MAX_THREADS=%DEFAULT_MAX_THREADS%"

if exist "%CONFIG_FILE%" (
    for /f "usebackq tokens=1,2 delims==" %%a in ("%CONFIG_FILE%") do (
        if "%%a"=="AE_PATH" set "AE_PATH=%%b"
        if "%%a"=="MAX_THREADS" set "MAX_THREADS=%%b"
    )
)
set "QUEUE_DIR=%~dp0queue"
set "LOG_DIR=%~dp0logs"
set "COMPLETED_DIR=%~dp0completed"
set "RUNNING_DIR=%~dp0running"

:: 创建必要的目录
if not exist "%RUNNING_DIR%" mkdir "%RUNNING_DIR%"

echo ========================================
echo    AE多线程渲染管理器
echo ========================================
echo 最大并发线程数: %MAX_THREADS%
echo 队列目录: %QUEUE_DIR%
echo 日志目录: %LOG_DIR%
echo ========================================
echo.

:MAIN_LOOP
:: 清屏并显示状态
cls
echo ========================================
echo    AE多线程渲染管理器 - 运行中
echo ========================================
echo 时间: %date% %time%
echo 最大并发线程数: %MAX_THREADS%
echo.

:: 统计当前状态
set "QUEUE_COUNT=0"
set "RUNNING_COUNT=0"
set "COMPLETED_COUNT=0"

for %%f in ("%QUEUE_DIR%\*.queue") do set /a QUEUE_COUNT+=1
for %%f in ("%RUNNING_DIR%\*.running") do set /a RUNNING_COUNT+=1
for %%f in ("%COMPLETED_DIR%\*.completed") do set /a COMPLETED_COUNT+=1

echo 队列中项目: %QUEUE_COUNT%
echo 正在渲染: %RUNNING_COUNT%
echo 已完成: %COMPLETED_COUNT%
echo.

:: 显示正在运行的任务
if %RUNNING_COUNT% gtr 0 (
    echo 正在渲染的项目:
    for %%f in ("%RUNNING_DIR%\*.running") do (
        set "running_file=%%~nf"
        echo   - !running_file!
    )
    echo.
)

:: 检查是否可以启动新的渲染任务
if %RUNNING_COUNT% lss %MAX_THREADS% (
    :: 查找队列中的第一个任务
    for %%f in ("%QUEUE_DIR%\*.queue") do (
        set "QUEUE_FILE=%%f"
        set "PROJECT_HASH=%%~nf"
        goto START_RENDER
    )
)

:: 清理已完成的运行标记
for %%f in ("%RUNNING_DIR%\*.running") do (
    set "running_hash=%%~nf"
    set "pid_file=%RUNNING_DIR%\!running_hash!.pid"
    
    if exist "!pid_file!" (
        set /p pid=<"!pid_file!"
        tasklist /FI "PID eq !pid!" 2>NUL | find "!pid!" >NUL
        if errorlevel 1 (
            echo 检测到渲染进程 !pid! 已结束，清理运行标记...
            del "%%f" 2>NUL
            del "!pid_file!" 2>NUL
            
            :: 检查是否成功完成
            call :CHECK_RENDER_RESULT "!running_hash!"
        )
    )
)

goto WAIT_AND_LOOP

:START_RENDER
if not defined QUEUE_FILE goto WAIT_AND_LOOP

echo 启动新的渲染任务: %PROJECT_HASH%

:: 读取队列文件信息
set "line_num=0"
for /f "usebackq delims=" %%a in ("%QUEUE_FILE%") do (
    set /a line_num+=1
    if !line_num!==1 set "queue_time=%%a"
    if !line_num!==2 set "project_path=%%a"
)

:: 移动到运行目录
set "RUNNING_FILE=%RUNNING_DIR%\%PROJECT_HASH%.running"
move "%QUEUE_FILE%" "%RUNNING_FILE%" >NUL

:: 创建日志文件
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "timestamp=%dt:~0,4%-%dt:~4,2%-%dt:~6,2%_%dt:~8,2%-%dt:~10,2%-%dt:~12,2%"
set "LOG_FILE=%LOG_DIR%\%PROJECT_HASH%_%timestamp%.log"

:: 启动渲染进程
echo 开始渲染: %project_path%
echo 日志文件: %LOG_FILE%
start "AE渲染_%PROJECT_HASH%" /min cmd /c ""%AE_PATH%" -project %project_path% > "%LOG_FILE%" 2>&1 & echo 渲染完成时间: %date% %time% >> "%LOG_FILE%""

:: 获取进程ID并保存
timeout /t 2 >NUL
for /f "tokens=2" %%a in ('tasklist /FI "WINDOWTITLE eq AE渲染_%PROJECT_HASH%" /FO CSV ^| find "cmd.exe"') do (
    set "render_pid=%%a"
    set "render_pid=!render_pid:"=!"
    echo !render_pid!>"%RUNNING_DIR%\%PROJECT_HASH%.pid"
)

goto MAIN_LOOP

:CHECK_RENDER_RESULT
set "check_hash=%~1"
set "check_hash=%check_hash:"=%"

:: 检查日志文件中是否有错误
set "latest_log="
for /f "delims=" %%f in ('dir /b /o-d "%LOG_DIR%\%check_hash%_*.log" 2^>NUL') do (
    set "latest_log=%LOG_DIR%\%%f"
    goto check_log_content
)

:check_log_content
if not defined latest_log (
    echo 警告: 未找到项目 %check_hash% 的日志文件
    goto :eof
)

:: 简单检查是否成功完成
find /i "error" "%latest_log%" >NUL
if not errorlevel 1 (
    echo 渲染失败: %check_hash% （发现错误）
    echo 详细信息请查看: %latest_log%
) else (
    echo 渲染完成: %check_hash%
    echo %timestamp%>"%COMPLETED_DIR%\%check_hash%.completed"
    echo 日志: %latest_log%>>"%COMPLETED_DIR%\%check_hash%.completed"
)
goto :eof

:WAIT_AND_LOOP
echo 等待中... (按 Ctrl+C 退出)
timeout /t 5 >NUL
goto MAIN_LOOP
