#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AE分段渲染系统 - 图形界面
支持单个项目的多线程分段渲染
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import time
import os
from pathlib import Path
from ae_render_manager import AERenderManager

class AESegmentRenderGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("AE分段渲染系统 - 多线程加速渲染")
        self.root.geometry("900x700")
        
        # 创建渲染管理器
        self.manager = AERenderManager()
        self.manager_thread = None
        self.is_manager_running = False
        
        # 创建界面
        self.create_widgets()
        
        # 启动状态更新线程
        self.update_thread = threading.Thread(target=self.update_status_loop, daemon=True)
        self.update_thread.start()
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(5, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="AE分段渲染系统", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        subtitle_label = ttk.Label(main_frame, text="将单个AE项目分段并行渲染，大幅提升渲染速度", font=("Arial", 10))
        subtitle_label.grid(row=1, column=0, columnspan=3, pady=(0, 20))
        
        # 项目设置区域
        project_frame = ttk.LabelFrame(main_frame, text="项目设置", padding="10")
        project_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        project_frame.columnconfigure(1, weight=1)
        
        # 项目文件选择
        ttk.Label(project_frame, text="AE项目:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.project_path_var = tk.StringVar()
        project_entry = ttk.Entry(project_frame, textvariable=self.project_path_var, width=50)
        project_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        ttk.Button(project_frame, text="浏览", command=self.browse_project).grid(row=0, column=2)
        
        # 渲染参数
        ttk.Label(project_frame, text="总帧数:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(10, 0))
        self.total_frames_var = tk.StringVar(value="300")
        frames_entry = ttk.Entry(project_frame, textvariable=self.total_frames_var, width=10)
        frames_entry.grid(row=1, column=1, sticky=tk.W, pady=(10, 0))
        
        ttk.Label(project_frame, text="帧率:").grid(row=2, column=0, sticky=tk.W, padx=(0, 10), pady=(5, 0))
        self.fps_var = tk.StringVar(value="25")
        fps_entry = ttk.Entry(project_frame, textvariable=self.fps_var, width=10)
        fps_entry.grid(row=2, column=1, sticky=tk.W, pady=(5, 0))
        
        # 配置区域
        config_frame = ttk.LabelFrame(main_frame, text="渲染配置", padding="10")
        config_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        config_frame.columnconfigure(1, weight=1)
        
        # CPU信息
        cpu_count = self.manager.get_cpu_count()
        recommended = self.manager.get_recommended_threads()
        ttk.Label(config_frame, text=f"CPU核心数: {cpu_count} | 推荐线程数: {recommended}").grid(
            row=0, column=0, columnspan=3, sticky=tk.W, pady=(0, 10))
        
        # 线程数设置
        ttk.Label(config_frame, text="渲染线程数:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10))
        self.max_threads_var = tk.StringVar(value=str(self.manager.config["max_threads"]))
        threads_spinbox = ttk.Spinbox(config_frame, from_=1, to=32, textvariable=self.max_threads_var, width=10)
        threads_spinbox.grid(row=1, column=1, sticky=tk.W)
        
        ttk.Button(config_frame, text="使用推荐值", command=self.use_recommended).grid(row=1, column=2, padx=(10, 0))
        
        # AE路径
        ttk.Label(config_frame, text="AE路径:").grid(row=2, column=0, sticky=tk.W, padx=(0, 10), pady=(10, 0))
        self.ae_path_var = tk.StringVar(value=self.manager.config["ae_path"])
        ae_path_entry = ttk.Entry(config_frame, textvariable=self.ae_path_var, width=50)
        ae_path_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), padx=(0, 10), pady=(10, 0))
        ttk.Button(config_frame, text="浏览", command=self.browse_ae_path).grid(row=2, column=2, pady=(10, 0))
        
        # 操作区域
        operation_frame = ttk.LabelFrame(main_frame, text="操作", padding="10")
        operation_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 按钮
        ttk.Button(operation_frame, text="添加项目到队列", command=self.add_project).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(operation_frame, text="开始分段渲染", command=self.start_manager).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(operation_frame, text="停止渲染", command=self.stop_manager).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(operation_frame, text="保存配置", command=self.save_config).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(operation_frame, text="清理输出", command=self.clear_output).pack(side=tk.LEFT, padx=(0, 10))
        
        # 状态区域
        status_frame = ttk.LabelFrame(main_frame, text="渲染状态", padding="10")
        status_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        status_frame.columnconfigure(0, weight=1)
        status_frame.rowconfigure(1, weight=1)
        
        # 状态信息
        status_info_frame = ttk.Frame(status_frame)
        status_info_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        status_info_frame.columnconfigure(1, weight=1)
        status_info_frame.columnconfigure(3, weight=1)
        status_info_frame.columnconfigure(5, weight=1)
        
        ttk.Label(status_info_frame, text="队列中:").grid(row=0, column=0, sticky=tk.W)
        self.queue_count_var = tk.StringVar(value="0")
        ttk.Label(status_info_frame, textvariable=self.queue_count_var, font=("Arial", 12, "bold")).grid(row=0, column=1, sticky=tk.W, padx=(5, 20))
        
        ttk.Label(status_info_frame, text="正在渲染:").grid(row=0, column=2, sticky=tk.W)
        self.running_count_var = tk.StringVar(value="0")
        ttk.Label(status_info_frame, textvariable=self.running_count_var, font=("Arial", 12, "bold")).grid(row=0, column=3, sticky=tk.W, padx=(5, 20))
        
        ttk.Label(status_info_frame, text="已完成:").grid(row=0, column=4, sticky=tk.W)
        self.completed_count_var = tk.StringVar(value="0")
        ttk.Label(status_info_frame, textvariable=self.completed_count_var, font=("Arial", 12, "bold")).grid(row=0, column=5, sticky=tk.W, padx=(5, 0))
        
        # 管理器状态
        ttk.Label(status_info_frame, text="状态:").grid(row=1, column=0, sticky=tk.W, pady=(10, 0))
        self.manager_status_var = tk.StringVar(value="已停止")
        status_label = ttk.Label(status_info_frame, textvariable=self.manager_status_var, font=("Arial", 12, "bold"))
        status_label.grid(row=1, column=1, sticky=tk.W, padx=(5, 0), pady=(10, 0))
        
        # 详细信息
        self.detail_text = scrolledtext.ScrolledText(status_frame, height=15)
        self.detail_text.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
    
    def browse_project(self):
        """浏览AE项目文件"""
        filename = filedialog.askopenfilename(
            title="选择AE项目文件",
            filetypes=[("AE项目文件", "*.aep"), ("所有文件", "*.*")]
        )
        if filename:
            self.project_path_var.set(filename)
    
    def browse_ae_path(self):
        """浏览AE路径"""
        filename = filedialog.askopenfilename(
            title="选择AE渲染器",
            filetypes=[("可执行文件", "*.exe"), ("所有文件", "*.*")]
        )
        if filename:
            self.ae_path_var.set(filename)
    
    def use_recommended(self):
        """使用推荐的线程数"""
        recommended = self.manager.get_recommended_threads()
        self.max_threads_var.set(str(recommended))
        self.log(f"已设置为推荐线程数: {recommended}")
    
    def save_config(self):
        """保存配置"""
        try:
            self.manager.config["ae_path"] = self.ae_path_var.get()
            self.manager.config["max_threads"] = int(self.max_threads_var.get())
            
            if self.manager.save_config():
                messagebox.showinfo("成功", "配置已保存")
                self.log("配置已保存")
            else:
                messagebox.showerror("错误", "配置保存失败")
        except ValueError:
            messagebox.showerror("错误", "线程数必须是数字")
    
    def add_project(self):
        """添加项目到分段渲染队列"""
        project_path = self.project_path_var.get().strip()
        if not project_path:
            messagebox.showerror("错误", "请选择AE项目文件")
            return
        
        try:
            total_frames = int(self.total_frames_var.get())
            fps = int(self.fps_var.get())
        except ValueError:
            messagebox.showerror("错误", "总帧数和帧率必须是数字")
            return
        
        success, message = self.manager.add_project_to_queue(project_path, total_frames, fps)
        if success:
            self.log(f"✓ {message}")
            messagebox.showinfo("成功", message)
        else:
            self.log(f"✗ {message}")
            messagebox.showerror("错误", message)
    
    def start_manager(self):
        """启动分段渲染管理器"""
        if not self.is_manager_running:
            self.manager_thread = threading.Thread(target=self.run_manager, daemon=True)
            self.manager_thread.start()
            self.is_manager_running = True
            self.log("分段渲染管理器已启动")
    
    def stop_manager(self):
        """停止分段渲染管理器"""
        if self.is_manager_running:
            self.log("正在停止渲染管理器...")
            self.manager.stop_manager()
            self.is_manager_running = False
            self.log("渲染管理器已停止")
    
    def run_manager(self):
        """运行渲染管理器"""
        try:
            self.manager.start_manager()
        except Exception as e:
            self.log(f"管理器运行错误: {e}")
        finally:
            self.is_manager_running = False
    
    def clear_output(self):
        """清理输出文件"""
        if messagebox.askyesno("确认", "确定要清理所有输出文件吗？"):
            try:
                output_dir = self.manager.base_dir / "output"
                if output_dir.exists():
                    import shutil
                    shutil.rmtree(output_dir)
                    output_dir.mkdir()
                self.log("输出文件已清理")
            except Exception as e:
                self.log(f"清理失败: {e}")
    
    def log(self, message):
        """添加日志"""
        timestamp = time.strftime("%H:%M:%S")
        self.detail_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.detail_text.see(tk.END)
    
    def update_status_loop(self):
        """状态更新循环"""
        while True:
            try:
                self.update_status()
                time.sleep(2)
            except Exception as e:
                print(f"状态更新错误: {e}")
                time.sleep(5)
    
    def update_status(self):
        """更新状态显示"""
        status = self.manager.get_status()
        
        # 更新计数
        self.queue_count_var.set(str(status["queue_count"]))
        self.running_count_var.set(str(status["running_count"]))
        self.completed_count_var.set(str(status["completed_count"]))
        
        # 更新管理器状态
        if self.is_manager_running:
            self.manager_status_var.set(f"运行中 ({status['running_count']}/{status['max_threads']})")
        else:
            self.manager_status_var.set("已停止")
    
    def run(self):
        """运行GUI"""
        self.root.mainloop()


if __name__ == "__main__":
    app = AESegmentRenderGUI()
    app.run()
