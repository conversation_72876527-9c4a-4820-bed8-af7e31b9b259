@echo off
setlocal enabledelayedexpansion

echo ========================================
echo      AE渲染系统配置工具
echo ========================================
echo.

:: 当前配置文件路径
set "CONFIG_FILE=%~dp0config.ini"

:: 默认配置
set "DEFAULT_AE_PATH=D:\soft\AE2023\Adobe After Effects 2023\Support Files\aerender.exe"
set "DEFAULT_MAX_THREADS=4"

:: 读取现有配置
if exist "%CONFIG_FILE%" (
    for /f "usebackq tokens=1,2 delims==" %%a in ("%CONFIG_FILE%") do (
        if "%%a"=="AE_PATH" set "CURRENT_AE_PATH=%%b"
        if "%%a"=="MAX_THREADS" set "CURRENT_MAX_THREADS=%%b"
    )
) else (
    set "CURRENT_AE_PATH=%DEFAULT_AE_PATH%"
    set "CURRENT_MAX_THREADS=%DEFAULT_MAX_THREADS%"
)

:MAIN_MENU
cls
echo ========================================
echo      AE渲染系统配置工具
echo ========================================
echo.
echo 当前配置:
echo   AE路径: %CURRENT_AE_PATH%
echo   最大线程数: %CURRENT_MAX_THREADS%
echo.
echo 配置选项:
echo   1. 修改AE路径
echo   2. 修改最大线程数
echo   3. 保存配置
echo   4. 恢复默认配置
echo   5. 测试AE路径
echo   6. 退出
echo.
set /p choice=请选择操作 (1-6): 

if "%choice%"=="1" goto CHANGE_AE_PATH
if "%choice%"=="2" goto CHANGE_MAX_THREADS  
if "%choice%"=="3" goto SAVE_CONFIG
if "%choice%"=="4" goto RESTORE_DEFAULT
if "%choice%"=="5" goto TEST_AE_PATH
if "%choice%"=="6" exit /b
goto MAIN_MENU

:CHANGE_AE_PATH
echo.
echo 当前AE路径: %CURRENT_AE_PATH%
echo.
echo 请输入新的AE路径 (aerender.exe的完整路径):
set /p new_path=
if not "%new_path%"=="" (
    set "CURRENT_AE_PATH=%new_path%"
    echo AE路径已更新为: %new_path%
) else (
    echo 未修改AE路径
)
pause
goto MAIN_MENU

:CHANGE_MAX_THREADS
echo.
echo 当前最大线程数: %CURRENT_MAX_THREADS%
echo.
echo 建议设置:
echo   - CPU核心数较少 (4核以下): 2-3
echo   - CPU核心数中等 (4-8核): 3-5  
echo   - CPU核心数较多 (8核以上): 4-8
echo.
echo 注意: 线程数过多可能导致系统卡顿，过少则无法充分利用性能
echo.
set /p new_threads=请输入新的最大线程数 (1-16): 

:: 验证输入
if "%new_threads%"=="" goto CHANGE_MAX_THREADS
set /a test_num=%new_threads% 2>NUL
if %test_num% lss 1 (
    echo 错误: 线程数必须大于等于1
    pause
    goto CHANGE_MAX_THREADS
)
if %test_num% gtr 16 (
    echo 错误: 线程数不建议超过16
    pause
    goto CHANGE_MAX_THREADS
)

set "CURRENT_MAX_THREADS=%new_threads%"
echo 最大线程数已更新为: %new_threads%
pause
goto MAIN_MENU

:SAVE_CONFIG
echo.
echo 正在保存配置到: %CONFIG_FILE%
(
    echo AE_PATH=%CURRENT_AE_PATH%
    echo MAX_THREADS=%CURRENT_MAX_THREADS%
) > "%CONFIG_FILE%"

if exist "%CONFIG_FILE%" (
    echo 配置保存成功！
) else (
    echo 配置保存失败！
)
pause
goto MAIN_MENU

:RESTORE_DEFAULT
echo.
set /p confirm=确定要恢复默认配置吗? (Y/N): 
if /i "%confirm%"=="Y" (
    set "CURRENT_AE_PATH=%DEFAULT_AE_PATH%"
    set "CURRENT_MAX_THREADS=%DEFAULT_MAX_THREADS%"
    echo 已恢复默认配置
) else (
    echo 取消恢复操作
)
pause
goto MAIN_MENU

:TEST_AE_PATH
echo.
echo 正在测试AE路径: %CURRENT_AE_PATH%
echo.

if not exist "%CURRENT_AE_PATH%" (
    echo 错误: 文件不存在！
    echo 请检查路径是否正确
    pause
    goto MAIN_MENU
)

echo 文件存在，正在测试运行...
"%CURRENT_AE_PATH%" -help >NUL 2>&1
if errorlevel 1 (
    echo 警告: 运行测试失败，可能不是有效的aerender.exe文件
) else (
    echo 测试成功: AE路径配置正确！
)

pause
goto MAIN_MENU
