@echo off
chcp 65001 >nul
title AE多线程渲染系统

echo ========================================
echo    AE多线程渲染系统 (Python版)
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python
    echo 请安装Python 3.6或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

:: 显示Python版本
echo Python版本:
python --version

:: 检查必要文件
if not exist "ae_render_manager.py" (
    echo 错误: 缺少核心文件 ae_render_manager.py
    pause
    exit /b 1
)

if not exist "启动渲染系统.py" (
    echo 错误: 缺少启动文件 启动渲染系统.py
    pause
    exit /b 1
)

echo.
echo 启动Python渲染系统...
echo.

:: 启动Python程序
python "启动渲染系统.py"

echo.
echo 程序已退出
pause
