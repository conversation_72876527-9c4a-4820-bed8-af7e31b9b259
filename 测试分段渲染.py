#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AE分段渲染功能
"""

import time
from pathlib import Path
from ae_render_manager import AERenderManager

def test_segment_calculation():
    """测试分段计算功能"""
    print("="*50)
    print("测试分段计算功能")
    print("="*50)
    
    manager = AERenderManager()
    
    # 测试不同的帧数和线程数组合
    test_cases = [
        (100, 4),   # 100帧，4线程
        (300, 8),   # 300帧，8线程
        (50, 8),    # 50帧，8线程（帧数少于线程数）
        (1000, 6),  # 1000帧，6线程
    ]
    
    for total_frames, num_threads in test_cases:
        print(f"\n总帧数: {total_frames}, 线程数: {num_threads}")
        segments = manager.calculate_segments(total_frames, num_threads)
        
        print("分段结果:")
        total_check = 0
        for i, (start, end) in enumerate(segments):
            frames_in_segment = end - start + 1
            total_check += frames_in_segment
            print(f"  线程{i+1}: 帧 {start}-{end} ({frames_in_segment}帧)")
        
        print(f"验证: 总帧数 {total_check} {'✓' if total_check == total_frames else '✗'}")

def test_add_project():
    """测试添加项目功能"""
    print("\n" + "="*50)
    print("测试添加项目功能")
    print("="*50)
    
    manager = AERenderManager()
    
    # 查找测试项目
    test_projects = list(Path(".").glob("*.aep"))
    if not test_projects:
        print("未找到AE项目文件，跳过测试")
        return
    
    test_project = test_projects[0]
    print(f"使用测试项目: {test_project}")
    
    # 测试添加项目
    total_frames = 120  # 测试120帧
    fps = 25
    
    print(f"添加项目: {total_frames}帧, {fps}fps")
    success, message = manager.add_project_to_queue(test_project, total_frames, fps)
    
    print(f"结果: {message}")
    
    if success:
        # 显示分段信息
        print("\n分段信息:")
        segments = manager.calculate_segments(total_frames, manager.config["max_threads"])
        for i, (start, end) in enumerate(segments):
            print(f"  分段{i+1}: 帧 {start}-{end}")

def test_process_management():
    """测试进程管理功能"""
    print("\n" + "="*50)
    print("测试进程管理功能")
    print("="*50)
    
    manager = AERenderManager()
    
    print("测试进程终止功能...")
    print("这将测试改进的进程管理，确保能正确终止所有AE进程")
    
    # 显示当前配置
    print(f"CPU核心数: {manager.get_cpu_count()}")
    print(f"推荐线程数: {manager.get_recommended_threads()}")
    print(f"当前线程数: {manager.config['max_threads']}")
    
    # 检查AE路径
    ae_path = Path(manager.config["ae_path"])
    if ae_path.exists():
        print(f"✓ AE路径有效: {ae_path}")
    else:
        print(f"✗ AE路径无效: {ae_path}")
        return
    
    print("\n进程管理改进:")
    print("1. 使用taskkill /F /T 终止进程树")
    print("2. 额外清理所有AE相关进程")
    print("3. 分段渲染支持独立进程管理")
    print("4. 改进的信号处理和退出清理")

def show_system_info():
    """显示系统信息"""
    print("="*50)
    print("AE分段渲染系统信息")
    print("="*50)
    
    manager = AERenderManager()
    
    print("系统配置:")
    print(f"  CPU核心数: {manager.get_cpu_count()}")
    print(f"  推荐线程数: {manager.get_recommended_threads()}")
    print(f"  当前线程数: {manager.config['max_threads']}")
    print(f"  自动检测CPU: {manager.config.get('auto_detect_cpu', True)}")
    
    print(f"\nAE配置:")
    print(f"  AE路径: {manager.config['ae_path']}")
    ae_exists = Path(manager.config['ae_path']).exists()
    print(f"  路径有效: {'✓' if ae_exists else '✗'}")
    
    print(f"\n目录结构:")
    dirs = ["queue", "running", "completed", "logs"]
    for dir_name in dirs:
        dir_path = getattr(manager, f"{dir_name}_dir")
        file_count = len(list(dir_path.glob("*")))
        print(f"  {dir_name}: {file_count} 个文件")
    
    # 检查输出目录
    output_dir = manager.base_dir / "output"
    if output_dir.exists():
        output_count = len(list(output_dir.glob("*")))
        print(f"  output: {output_count} 个文件")
    else:
        print(f"  output: 目录不存在")

def main():
    """主函数"""
    print("AE分段渲染系统测试")
    print("="*30)
    
    tests = [
        ("1", "显示系统信息", show_system_info),
        ("2", "测试分段计算", test_segment_calculation),
        ("3", "测试添加项目", test_add_project),
        ("4", "测试进程管理", test_process_management),
        ("5", "运行所有测试", None),
        ("6", "启动图形界面", None),
        ("0", "退出", None)
    ]
    
    while True:
        print("\n选择测试项目:")
        for code, title, _ in tests:
            print(f"  {code}. {title}")
        
        choice = input("\n请选择 (0-6): ").strip()
        
        if choice == "0":
            break
        elif choice == "5":
            # 运行所有测试
            for code, title, func in tests:
                if func:
                    print(f"\n{'='*20} {title} {'='*20}")
                    func()
        elif choice == "6":
            # 启动图形界面
            print("启动分段渲染图形界面...")
            try:
                from ae_segment_render_gui import AESegmentRenderGUI
                app = AESegmentRenderGUI()
                app.run()
                break
            except Exception as e:
                print(f"启动图形界面失败: {e}")
        else:
            for code, title, func in tests:
                if choice == code and func:
                    func()
                    break
            else:
                print("无效选择")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n测试已取消")
    except Exception as e:
        print(f"\n测试过程中出错: {e}")
    finally:
        input("\n按回车键退出...")
