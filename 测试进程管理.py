#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AE渲染系统的进程管理功能
"""

import time
import signal
import sys
from ae_render_manager import AERenderManager

def test_process_management():
    """测试进程管理功能"""
    print("="*60)
    print("AE渲染系统进程管理测试")
    print("="*60)
    
    # 创建管理器
    manager = AERenderManager()
    
    # 显示系统信息
    print(f"CPU核心数: {manager.get_cpu_count()}")
    print(f"推荐线程数: {manager.get_recommended_threads()}")
    print(f"当前配置线程数: {manager.config['max_threads']}")
    print(f"自动检测CPU: {manager.config.get('auto_detect_cpu', True)}")
    print()
    
    # 检查AE路径
    ae_path = manager.config['ae_path']
    print(f"AE路径: {ae_path}")
    
    from pathlib import Path
    if Path(ae_path).exists():
        print("✓ AE路径有效")
    else:
        print("✗ AE路径无效")
        print("请先配置正确的AE路径")
        return
    
    print()
    
    # 查找测试项目
    test_projects = list(Path(".").glob("*.aep"))
    if not test_projects:
        print("未找到AE项目文件(.aep)")
        print("请在当前目录放置一个AE项目文件进行测试")
        return
    
    test_project = test_projects[0]
    print(f"使用测试项目: {test_project}")
    print()
    
    # 添加项目到队列
    print("1. 测试添加项目到队列...")
    success, message = manager.add_project_to_queue(test_project)
    print(f"结果: {message}")
    
    if not success:
        print("添加项目失败，测试结束")
        return
    
    print()
    print("2. 测试进程管理...")
    print("将启动渲染管理器，请在5秒后按 Ctrl+C 测试进程终止功能")
    print("观察后台是否还有AE进程残留")
    
    # 设置信号处理
    def signal_handler(signum, frame):
        print(f"\n接收到信号 {signum}")
        print("测试进程终止功能...")
        manager.stop_manager()
        
        # 检查是否还有AE进程
        import subprocess
        try:
            if sys.platform == "win32":
                result = subprocess.run(
                    ["tasklist", "/FI", "IMAGENAME eq aerender.exe"],
                    capture_output=True, text=True
                )
                if "aerender.exe" in result.stdout:
                    print("警告: 检测到残留的AE进程")
                    print(result.stdout)
                else:
                    print("✓ 没有检测到残留的AE进程")
            else:
                result = subprocess.run(
                    ["pgrep", "-f", "aerender"],
                    capture_output=True, text=True
                )
                if result.stdout.strip():
                    print("警告: 检测到残留的AE进程")
                    print(f"PID: {result.stdout.strip()}")
                else:
                    print("✓ 没有检测到残留的AE进程")
        except Exception as e:
            print(f"检查进程时出错: {e}")
        
        print("测试完成")
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    
    try:
        # 启动管理器
        manager.start_manager()
    except KeyboardInterrupt:
        pass

def test_cpu_detection():
    """测试CPU检测功能"""
    print("="*60)
    print("CPU检测功能测试")
    print("="*60)
    
    manager = AERenderManager()
    
    print(f"检测到的CPU核心数: {manager.get_cpu_count()}")
    print(f"推荐的线程数: {manager.get_recommended_threads()}")
    
    # 测试不同的线程数建议
    cpu_counts = [2, 4, 6, 8, 12, 16, 20, 24, 32]
    print("\n不同CPU核心数的线程数建议:")
    for cpu in cpu_counts:
        recommended = max(1, min(int(cpu * 0.75), 8))
        print(f"  {cpu}核 -> 推荐{recommended}线程")
    
    print(f"\n当前配置:")
    print(f"  最大线程数: {manager.config['max_threads']}")
    print(f"  自动检测CPU: {manager.config.get('auto_detect_cpu', True)}")

def main():
    """主函数"""
    print("选择测试项目:")
    print("1. 测试进程管理功能")
    print("2. 测试CPU检测功能")
    print("3. 退出")
    
    choice = input("\n请选择 (1-3): ").strip()
    
    if choice == "1":
        test_process_management()
    elif choice == "2":
        test_cpu_detection()
    elif choice == "3":
        print("退出测试")
    else:
        print("无效选择")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"测试过程中出错: {e}")
        input("按回车键退出...")
