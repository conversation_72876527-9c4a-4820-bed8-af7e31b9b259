@echo off
setlocal enabledelayedexpansion

echo ========================================
echo      AE渲染系统测试工具
echo ========================================
echo.

:: 检查必要的目录
set "QUEUE_DIR=%~dp0queue"
set "LOG_DIR=%~dp0logs"
set "COMPLETED_DIR=%~dp0completed"
set "RUNNING_DIR=%~dp0running"

echo 正在检查系统组件...
echo.

:: 检查脚本文件
set "missing_files="
if not exist "%~dp0渲染.bat" set "missing_files=!missing_files! 渲染.bat"
if not exist "%~dp0渲染管理器.bat" set "missing_files=!missing_files! 渲染管理器.bat"
if not exist "%~dp0查看渲染状态.bat" set "missing_files=!missing_files! 查看渲染状态.bat"
if not exist "%~dp0配置.bat" set "missing_files=!missing_files! 配置.bat"

if not "%missing_files%"=="" (
    echo 错误: 缺少必要文件:%missing_files%
    pause
    exit /b 1
)

echo ✓ 所有脚本文件存在

:: 创建测试目录
echo 正在创建必要目录...
if not exist "%QUEUE_DIR%" mkdir "%QUEUE_DIR%"
if not exist "%LOG_DIR%" mkdir "%LOG_DIR%"
if not exist "%COMPLETED_DIR%" mkdir "%COMPLETED_DIR%"
if not exist "%RUNNING_DIR%" mkdir "%RUNNING_DIR%"

echo ✓ 目录结构创建完成

:: 检查配置
set "CONFIG_FILE=%~dp0config.ini"
if exist "%CONFIG_FILE%" (
    echo ✓ 配置文件存在
    echo 当前配置:
    type "%CONFIG_FILE%"
) else (
    echo ! 配置文件不存在，将使用默认配置
    echo 建议运行"配置.bat"进行初始设置
)

echo.

:: 检查AE路径
set "DEFAULT_AE_PATH=D:\soft\AE2023\Adobe After Effects 2023\Support Files\aerender.exe"
set "AE_PATH=%DEFAULT_AE_PATH%"

if exist "%CONFIG_FILE%" (
    for /f "usebackq tokens=1,2 delims==" %%a in ("%CONFIG_FILE%") do (
        if "%%a"=="AE_PATH" set "AE_PATH=%%b"
    )
)

echo 检查AE路径: %AE_PATH%
if exist "%AE_PATH%" (
    echo ✓ AE路径有效
) else (
    echo ✗ AE路径无效，请运行"配置.bat"设置正确路径
)

echo.

:: 检查测试项目文件
echo 检查测试项目文件...
set "test_project="
for %%f in ("%~dp0*.aep") do (
    set "test_project=%%f"
    goto found_project
)

:found_project
if defined test_project (
    echo ✓ 找到测试项目: %test_project%
) else (
    echo ! 未找到AE项目文件(.aep)
    echo 请确保有AE项目文件用于测试
)

echo.
echo ========================================
echo 测试选项:
echo   1. 运行配置工具
echo   2. 测试添加项目到队列
echo   3. 查看系统状态
echo   4. 启动渲染管理器
echo   5. 清理测试数据
echo   6. 退出
echo ========================================
set /p choice=请选择测试操作 (1-6): 

if "%choice%"=="1" goto RUN_CONFIG
if "%choice%"=="2" goto TEST_QUEUE
if "%choice%"=="3" goto VIEW_STATUS
if "%choice%"=="4" goto START_MANAGER
if "%choice%"=="5" goto CLEAN_TEST
if "%choice%"=="6" exit /b
goto :eof

:RUN_CONFIG
echo.
echo 启动配置工具...
call "%~dp0配置.bat"
goto :eof

:TEST_QUEUE
if not defined test_project (
    echo 错误: 没有找到测试项目文件
    pause
    goto :eof
)

echo.
echo 测试添加项目到队列: %test_project%
call "%~dp0渲染.bat" "%test_project%"
goto :eof

:VIEW_STATUS
echo.
echo 启动状态查看器...
call "%~dp0查看渲染状态.bat"
goto :eof

:START_MANAGER
echo.
echo 启动渲染管理器...
start "渲染管理器测试" cmd /c ""%~dp0渲染管理器.bat""
echo 渲染管理器已在新窗口中启动
pause
goto :eof

:CLEAN_TEST
echo.
echo 清理测试数据...
del "%QUEUE_DIR%\*.queue" 2>NUL
del "%LOG_DIR%\*.log" 2>NUL
del "%COMPLETED_DIR%\*.completed" 2>NUL
del "%RUNNING_DIR%\*.running" 2>NUL
del "%RUNNING_DIR%\*.pid" 2>NUL
echo 测试数据已清理
pause
goto :eof
