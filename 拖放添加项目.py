#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AE项目拖放添加工具
将AE项目文件拖放到此脚本上即可添加到渲染队列
"""

import sys
import os
import time
from pathlib import Path
from ae_render_manager import AERenderManager

def main():
    print("="*60)
    print("AE多线程渲染系统 - 项目添加工具")
    print("="*60)
    
    # 创建管理器
    manager = AERenderManager()
    
    if len(sys.argv) > 1:
        # 有文件被拖放
        project_files = sys.argv[1:]
        
        print(f"检测到 {len(project_files)} 个文件:")
        for file_path in project_files:
            print(f"  - {file_path}")
        print()
        
        # 添加每个文件到队列
        added_count = 0
        for file_path in project_files:
            file_path = Path(file_path)
            
            # 检查文件是否存在
            if not file_path.exists():
                print(f"✗ 文件不存在: {file_path}")
                continue
            
            # 检查文件扩展名
            if file_path.suffix.lower() != '.aep':
                print(f"✗ 不是AE项目文件: {file_path}")
                continue
            
            # 添加到队列
            success, message = manager.add_project_to_queue(file_path)
            if success:
                print(f"✓ {message}")
                added_count += 1
            else:
                print(f"✗ {message}")
        
        print()
        print(f"成功添加 {added_count} 个项目到渲染队列")
        
        # 显示当前状态
        status = manager.get_status()
        print(f"当前队列状态: {status['queue_count']} 个项目等待渲染")
        
        # 询问是否启动渲染管理器
        if status['queue_count'] > 0:
            print()
            choice = input("是否启动渲染管理器? (Y/n): ").strip().lower()
            if choice != 'n':
                print("\n启动渲染管理器...")
                print("按 Ctrl+C 停止管理器")
                try:
                    manager.start_manager()
                except KeyboardInterrupt:
                    print("\n渲染管理器已停止")
    
    else:
        # 没有文件被拖放，显示使用说明
        print("使用方法:")
        print("1. 将AE项目文件(.aep)拖放到此脚本上")
        print("2. 或者双击此脚本进入交互模式")
        print()
        
        # 显示当前状态
        status = manager.get_status()
        print("当前状态:")
        print(f"  队列中: {status['queue_count']} 个项目")
        print(f"  正在渲染: {status['running_count']} 个项目")
        print(f"  已完成: {status['completed_count']} 个项目")
        print()
        
        # 交互模式
        while True:
            print("操作选项:")
            print("1. 手动添加AE项目")
            print("2. 启动渲染管理器")
            print("3. 查看详细状态")
            print("4. 启动图形界面")
            print("5. 退出")
            
            choice = input("\n请选择操作 (1-5): ").strip()
            
            if choice == "1":
                file_path = input("请输入AE项目文件路径: ").strip()
                if file_path:
                    # 移除引号
                    file_path = file_path.strip('"\'')
                    success, message = manager.add_project_to_queue(file_path)
                    print(f"{'✓' if success else '✗'} {message}")
            
            elif choice == "2":
                print("\n启动渲染管理器...")
                print("按 Ctrl+C 停止管理器")
                try:
                    manager.start_manager()
                except KeyboardInterrupt:
                    print("\n渲染管理器已停止")
            
            elif choice == "3":
                # 显示详细状态
                status = manager.get_status()
                print(f"\n详细状态:")
                print(f"  队列中: {status['queue_count']} 个项目")
                print(f"  正在渲染: {status['running_count']} 个项目")
                print(f"  已完成: {status['completed_count']} 个项目")
                print(f"  最大线程数: {status['max_threads']}")
                
                # 显示队列详情
                if status['queue_count'] > 0:
                    print("\n队列中的项目:")
                    import json
                    for i, queue_file in enumerate(manager.queue_dir.glob("*.json"), 1):
                        try:
                            with open(queue_file, 'r', encoding='utf-8') as f:
                                info = json.load(f)
                            print(f"  {i}. {info['project_name']}")
                        except:
                            pass
                
                # 显示运行中的项目
                if status['running_count'] > 0:
                    print("\n正在渲染的项目:")
                    import json
                    for i, running_file in enumerate(manager.running_dir.glob("*.json"), 1):
                        try:
                            with open(running_file, 'r', encoding='utf-8') as f:
                                info = json.load(f)
                            print(f"  {i}. {info['project_name']}")
                        except:
                            pass
            
            elif choice == "4":
                print("\n启动图形界面...")
                try:
                    from ae_render_gui import AERenderGUI
                    app = AERenderGUI()
                    app.run()
                except ImportError:
                    print("图形界面模块不可用，请确保安装了tkinter")
                except Exception as e:
                    print(f"启动图形界面失败: {e}")
            
            elif choice == "5":
                break
            
            else:
                print("无效选择，请重新输入")
            
            print()
    
    print("\n按回车键退出...")
    input()

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"程序运行错误: {e}")
        print("\n按回车键退出...")
        input()
