@echo off
setlocal enabledelayedexpansion

echo ========================================
echo      AE渲染系统清理工具
echo ========================================
echo.

:: 目录定义
set "QUEUE_DIR=%~dp0queue"
set "LOG_DIR=%~dp0logs"
set "COMPLETED_DIR=%~dp0completed"
set "RUNNING_DIR=%~dp0running"

:: 统计文件数量
set "QUEUE_COUNT=0"
set "LOG_COUNT=0"
set "COMPLETED_COUNT=0"
set "RUNNING_COUNT=0"

for %%f in ("%QUEUE_DIR%\*.queue") do set /a QUEUE_COUNT+=1
for %%f in ("%LOG_DIR%\*.log") do set /a LOG_COUNT+=1
for %%f in ("%COMPLETED_DIR%\*.completed") do set /a COMPLETED_COUNT+=1
for %%f in ("%RUNNING_DIR%\*.running") do set /a RUNNING_COUNT+=1

echo 当前系统状态:
echo   队列文件: %QUEUE_COUNT% 个
echo   日志文件: %LOG_COUNT% 个
echo   完成记录: %COMPLETED_COUNT% 个
echo   运行标记: %RUNNING_COUNT% 个
echo.

:MAIN_MENU
echo ========================================
echo 清理选项:
echo   1. 清理所有日志文件
echo   2. 清理完成记录
echo   3. 清理队列（谨慎操作）
echo   4. 清理运行标记
echo   5. 清理所有（完全重置）
echo   6. 查看磁盘占用
echo   7. 退出
echo ========================================
set /p choice=请选择操作 (1-7): 

if "%choice%"=="1" goto CLEAN_LOGS
if "%choice%"=="2" goto CLEAN_COMPLETED
if "%choice%"=="3" goto CLEAN_QUEUE
if "%choice%"=="4" goto CLEAN_RUNNING
if "%choice%"=="5" goto CLEAN_ALL
if "%choice%"=="6" goto SHOW_DISK_USAGE
if "%choice%"=="7" exit /b
goto MAIN_MENU

:CLEAN_LOGS
echo.
echo 当前有 %LOG_COUNT% 个日志文件
if %LOG_COUNT% equ 0 (
    echo 没有日志文件需要清理
    pause
    goto MAIN_MENU
)

echo.
echo 日志文件列表:
dir /b "%LOG_DIR%\*.log" 2>NUL
echo.
set /p confirm=确定要删除所有日志文件吗? (Y/N): 
if /i "%confirm%"=="Y" (
    del "%LOG_DIR%\*.log" 2>NUL
    echo 日志文件已清理
) else (
    echo 取消清理操作
)
pause
goto MAIN_MENU

:CLEAN_COMPLETED
echo.
echo 当前有 %COMPLETED_COUNT% 个完成记录
if %COMPLETED_COUNT% equ 0 (
    echo 没有完成记录需要清理
    pause
    goto MAIN_MENU
)

echo.
echo 完成记录列表:
dir /b "%COMPLETED_DIR%\*.completed" 2>NUL
echo.
set /p confirm=确定要删除所有完成记录吗? (Y/N): 
if /i "%confirm%"=="Y" (
    del "%COMPLETED_DIR%\*.completed" 2>NUL
    echo 完成记录已清理
) else (
    echo 取消清理操作
)
pause
goto MAIN_MENU

:CLEAN_QUEUE
echo.
echo 警告: 清理队列将删除所有等待渲染的项目！
echo 当前有 %QUEUE_COUNT% 个队列文件
if %QUEUE_COUNT% equ 0 (
    echo 没有队列文件需要清理
    pause
    goto MAIN_MENU
)

echo.
echo 队列文件列表:
dir /b "%QUEUE_DIR%\*.queue" 2>NUL
echo.
set /p confirm=确定要删除所有队列文件吗? 这将取消所有等待的渲染任务! (Y/N): 
if /i "%confirm%"=="Y" (
    del "%QUEUE_DIR%\*.queue" 2>NUL
    echo 队列文件已清理
) else (
    echo 取消清理操作
)
pause
goto MAIN_MENU

:CLEAN_RUNNING
echo.
echo 当前有 %RUNNING_COUNT% 个运行标记
if %RUNNING_COUNT% equ 0 (
    echo 没有运行标记需要清理
    pause
    goto MAIN_MENU
)

echo.
echo 运行标记列表:
dir /b "%RUNNING_DIR%\*.running" 2>NUL
dir /b "%RUNNING_DIR%\*.pid" 2>NUL
echo.
echo 注意: 只有在确认没有渲染进程运行时才清理运行标记
set /p confirm=确定要清理运行标记吗? (Y/N): 
if /i "%confirm%"=="Y" (
    del "%RUNNING_DIR%\*.running" 2>NUL
    del "%RUNNING_DIR%\*.pid" 2>NUL
    echo 运行标记已清理
) else (
    echo 取消清理操作
)
pause
goto MAIN_MENU

:CLEAN_ALL
echo.
echo 警告: 这将删除所有系统文件，完全重置渲染系统！
echo 包括:
echo   - 所有日志文件 (%LOG_COUNT% 个)
echo   - 所有完成记录 (%COMPLETED_COUNT% 个)
echo   - 所有队列文件 (%QUEUE_COUNT% 个)
echo   - 所有运行标记 (%RUNNING_COUNT% 个)
echo.
set /p confirm1=确定要完全重置系统吗? (Y/N): 
if not /i "%confirm1%"=="Y" (
    echo 取消重置操作
    pause
    goto MAIN_MENU
)

echo.
set /p confirm2=再次确认: 这将删除所有数据，无法恢复! (Y/N): 
if /i "%confirm2%"=="Y" (
    echo 正在清理所有文件...
    del "%LOG_DIR%\*.log" 2>NUL
    del "%COMPLETED_DIR%\*.completed" 2>NUL
    del "%QUEUE_DIR%\*.queue" 2>NUL
    del "%RUNNING_DIR%\*.running" 2>NUL
    del "%RUNNING_DIR%\*.pid" 2>NUL
    echo 系统已完全重置
) else (
    echo 取消重置操作
)
pause
goto MAIN_MENU

:SHOW_DISK_USAGE
echo.
echo ========================================
echo 磁盘占用情况:
echo ========================================

if exist "%LOG_DIR%" (
    echo 日志目录:
    dir "%LOG_DIR%" | find "个文件"
    echo.
)

if exist "%COMPLETED_DIR%" (
    echo 完成记录目录:
    dir "%COMPLETED_DIR%" | find "个文件"
    echo.
)

if exist "%QUEUE_DIR%" (
    echo 队列目录:
    dir "%QUEUE_DIR%" | find "个文件"
    echo.
)

if exist "%RUNNING_DIR%" (
    echo 运行标记目录:
    dir "%RUNNING_DIR%" | find "个文件"
    echo.
)

echo 总体目录大小:
for /f "tokens=3" %%a in ('dir "%~dp0" /s /-c ^| find "个文件"') do (
    echo 系统总占用: %%a 字节
)

pause
goto MAIN_MENU
