#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AE多线程渲染系统功能演示
展示所有主要功能和改进点
"""

import time
import json
from pathlib import Path
from ae_render_manager import AERenderManager

def print_header(title):
    """打印标题"""
    print("\n" + "="*60)
    print(f"  {title}")
    print("="*60)

def print_section(title):
    """打印小节标题"""
    print(f"\n{title}")
    print("-" * len(title))

def demo_cpu_detection():
    """演示CPU自动检测功能"""
    print_header("CPU自动检测功能演示")
    
    manager = AERenderManager()
    
    print(f"✓ 自动检测CPU核心数: {manager.get_cpu_count()}")
    print(f"✓ 智能推荐线程数: {manager.get_recommended_threads()}")
    print(f"✓ 当前配置线程数: {manager.config['max_threads']}")
    print(f"✓ 自动检测开关: {manager.config.get('auto_detect_cpu', True)}")
    
    print("\n推荐算法: 使用CPU核心数的75%，最多8线程")
    print("这样既能充分利用性能，又避免系统过载")

def demo_queue_management():
    """演示队列管理功能"""
    print_header("智能队列管理演示")
    
    manager = AERenderManager()
    
    # 查找测试项目
    test_projects = list(Path(".").glob("*.aep"))
    if not test_projects:
        print("未找到AE项目文件，跳过队列管理演示")
        return
    
    test_project = test_projects[0]
    print(f"使用测试项目: {test_project}")
    
    print_section("1. 添加项目到队列")
    success, message = manager.add_project_to_queue(test_project)
    print(f"结果: {message}")
    
    print_section("2. 重复添加测试 (应该被拒绝)")
    success, message = manager.add_project_to_queue(test_project)
    print(f"结果: {message}")
    
    print_section("3. 查看队列状态")
    status = manager.get_status()
    print(f"队列中项目: {status['queue_count']}")
    print(f"正在渲染: {status['running_count']}")
    print(f"已完成: {status['completed_count']}")
    
    # 清理队列
    for queue_file in manager.queue_dir.glob("*.json"):
        queue_file.unlink()
    print("\n✓ 演示完成，已清理队列")

def demo_config_management():
    """演示配置管理功能"""
    print_header("配置管理功能演示")
    
    manager = AERenderManager()
    
    print_section("当前配置")
    config = manager.config
    for key, value in config.items():
        print(f"  {key}: {value}")
    
    print_section("配置文件位置")
    config_file = Path(manager.config_file)
    print(f"配置文件: {config_file.absolute()}")
    print(f"文件存在: {config_file.exists()}")
    
    if config_file.exists():
        print("\n配置文件内容:")
        with open(config_file, 'r', encoding='utf-8') as f:
            content = json.load(f)
        print(json.dumps(content, indent=2, ensure_ascii=False))

def demo_process_safety():
    """演示进程安全管理"""
    print_header("进程安全管理演示")
    
    print("✓ 信号处理: 注册了SIGINT和SIGTERM处理函数")
    print("✓ 退出清理: 使用atexit注册清理函数")
    print("✓ 进程终止: 优雅终止 -> 强制杀死的两阶段策略")
    print("✓ 状态清理: 自动清理运行状态文件")
    print("✓ 跨平台: 支持Windows和Unix系统的进程管理")
    
    print("\n进程终止策略:")
    print("1. 发送SIGTERM信号 (优雅终止)")
    print("2. 等待5秒让进程自然结束")
    print("3. 如果超时，发送SIGKILL强制终止")
    print("4. 清理相关的状态文件和记录")
    
    print("\n这完全解决了原版批处理脚本的进程残留问题！")

def demo_directory_structure():
    """演示目录结构"""
    print_header("目录结构演示")
    
    manager = AERenderManager()
    
    directories = [
        ("queue", "渲染队列目录"),
        ("running", "运行状态目录"), 
        ("completed", "完成记录目录"),
        ("logs", "日志文件目录")
    ]
    
    for dir_name, description in directories:
        dir_path = getattr(manager, f"{dir_name}_dir")
        file_count = len(list(dir_path.glob("*")))
        print(f"✓ {description}: {dir_path}")
        print(f"  文件数量: {file_count}")
        print(f"  目录存在: {dir_path.exists()}")

def demo_improvements():
    """演示相比原版的改进"""
    print_header("相比原版批处理的重大改进")
    
    improvements = [
        ("✅ 解决闪退问题", "使用Python重写，完全稳定"),
        ("✅ 真正多线程", "支持并行渲染，不再顺序执行"),
        ("✅ 智能队列管理", "避免重复渲染，自动排队"),
        ("✅ 进程安全管理", "彻底解决Ctrl+C后进程残留"),
        ("✅ 自动CPU检测", "智能推荐最佳线程数"),
        ("✅ 现代化界面", "图形界面 + 命令行双重支持"),
        ("✅ 详细状态监控", "实时显示渲染进度和状态"),
        ("✅ 完善日志系统", "详细记录渲染过程和错误"),
        ("✅ 灵活配置管理", "支持配置文件和实时调整"),
        ("✅ 多种使用方式", "拖放、GUI、CLI全支持")
    ]
    
    for improvement, description in improvements:
        print(f"{improvement}: {description}")

def main():
    """主演示函数"""
    print_header("AE多线程渲染系统 - 功能演示")
    print("本演示将展示系统的所有主要功能和改进点")
    
    demos = [
        ("1", "CPU自动检测功能", demo_cpu_detection),
        ("2", "智能队列管理", demo_queue_management),
        ("3", "配置管理功能", demo_config_management),
        ("4", "进程安全管理", demo_process_safety),
        ("5", "目录结构", demo_directory_structure),
        ("6", "重大改进总结", demo_improvements),
        ("A", "运行所有演示", None),
        ("Q", "退出", None)
    ]
    
    while True:
        print("\n" + "="*40)
        print("选择演示项目:")
        for code, title, _ in demos:
            print(f"  {code}. {title}")
        
        choice = input("\n请选择 (1-6/A/Q): ").strip().upper()
        
        if choice == "Q":
            break
        elif choice == "A":
            for code, title, func in demos:
                if func:
                    func()
            break
        else:
            for code, title, func in demos:
                if choice == code and func:
                    func()
                    break
            else:
                print("无效选择")
    
    print_header("演示完成")
    print("感谢使用AE多线程渲染系统！")
    print("现在你可以享受高效、稳定的AE渲染体验了！")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"演示过程中出错: {e}")
    finally:
        input("\n按回车键退出...")
