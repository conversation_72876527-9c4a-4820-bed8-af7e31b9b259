@echo off
setlocal enabledelayedexpansion

:: 默认配置参数
set "DEFAULT_AE_PATH=D:\soft\AE2023\Adobe After Effects 2023\Support Files\aerender.exe"
set "DEFAULT_MAX_THREADS=4"

:: 读取配置文件
set "CONFIG_FILE=%~dp0config.ini"
set "AE_PATH=%DEFAULT_AE_PATH%"
set "MAX_THREADS=%DEFAULT_MAX_THREADS%"

if exist "%CONFIG_FILE%" (
    for /f "usebackq tokens=1,2 delims==" %%a in ("%CONFIG_FILE%") do (
        if "%%a"=="AE_PATH" set "AE_PATH=%%b"
        if "%%a"=="MAX_THREADS" set "MAX_THREADS=%%b"
    )
)
set "QUEUE_DIR=%~dp0queue"
set "LOG_DIR=%~dp0logs"
set "COMPLETED_DIR=%~dp0completed"

:: 创建必要的目录
if not exist "%QUEUE_DIR%" mkdir "%QUEUE_DIR%"
if not exist "%LOG_DIR%" mkdir "%LOG_DIR%"
if not exist "%COMPLETED_DIR%" mkdir "%COMPLETED_DIR%"

:: 获取当前时间戳
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "timestamp=%dt:~0,4%-%dt:~4,2%-%dt:~6,2%_%dt:~8,2%-%dt:~10,2%-%dt:~12,2%"

:: 检查是否有输入文件
if "%~1"=="" (
    echo 请将AE项目文件拖放到此批处理文件上
    echo 或者运行渲染管理器查看当前队列状态
    call "%~dp0渲染管理器.bat"
    pause
    exit /b
)

:: 获取项目文件信息
set "PROJECT_FILE=%~1"
set "PROJECT_NAME=%~n1"
set "PROJECT_HASH="

:: 生成项目文件的简单哈希（基于文件名和大小）
for %%F in ("%PROJECT_FILE%") do (
    set "FILE_SIZE=%%~zF"
    set "PROJECT_HASH=%PROJECT_NAME%_!FILE_SIZE!"
)

:: 检查是否已经在队列中或已完成
set "QUEUE_FILE=%QUEUE_DIR%\!PROJECT_HASH!.queue"
set "COMPLETED_FILE=%COMPLETED_DIR%\!PROJECT_HASH!.completed"

if exist "%COMPLETED_FILE%" (
    echo 项目 "%PROJECT_NAME%" 已经渲染完成
    echo 完成时间:
    type "%COMPLETED_FILE%"
    echo.
    echo 如需重新渲染，请删除: %COMPLETED_FILE%
    pause
    exit /b
)

if exist "%QUEUE_FILE%" (
    echo 项目 "%PROJECT_NAME%" 已在渲染队列中
    echo 队列时间:
    type "%QUEUE_FILE%"
    pause
    exit /b
)

:: 添加到渲染队列
echo %timestamp%>"%QUEUE_FILE%"
echo "%PROJECT_FILE%">>"%QUEUE_FILE%"

echo 项目 "%PROJECT_NAME%" 已添加到渲染队列
echo 队列文件: %QUEUE_FILE%

:: 启动渲染管理器（如果还没有运行）
tasklist /FI "WINDOWTITLE eq 渲染管理器*" 2>NUL | find /I /N "cmd.exe">NUL
if "%ERRORLEVEL%"=="1" (
    echo 启动渲染管理器...
    start "渲染管理器" cmd /c ""%~dp0渲染管理器.bat""
)

echo 渲染已加入队列，管理器将自动处理
pause