#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AE多线程渲染系统 - 命令行工具
"""

import sys
import argparse
import json
import time
from pathlib import Path
from ae_render_manager import AERenderManager

def print_status(manager):
    """打印当前状态"""
    status = manager.get_status()
    
    print("\n" + "="*50)
    print("AE渲染系统状态")
    print("="*50)
    print(f"队列中项目: {status['queue_count']}")
    print(f"正在渲染: {status['running_count']}")
    print(f"已完成: {status['completed_count']}")
    print(f"最大线程数: {status['max_threads']}")
    print(f"管理器状态: {'运行中' if status['is_running'] else '已停止'}")
    print("="*50)

def print_queue_details(manager):
    """打印队列详情"""
    print("\n队列详情:")
    print("-" * 30)
    
    queue_files = list(manager.queue_dir.glob("*.json"))
    if not queue_files:
        print("队列为空")
        return
    
    for i, queue_file in enumerate(queue_files, 1):
        try:
            with open(queue_file, 'r', encoding='utf-8') as f:
                info = json.load(f)
            
            print(f"{i}. {info['project_name']}")
            print(f"   路径: {info['project_path']}")
            print(f"   添加时间: {info['added_time']}")
            print()
        except Exception as e:
            print(f"读取队列文件失败: {e}")

def print_running_details(manager):
    """打印运行详情"""
    print("\n运行中项目:")
    print("-" * 30)
    
    running_files = list(manager.running_dir.glob("*.json"))
    if not running_files:
        print("没有正在运行的项目")
        return
    
    for i, running_file in enumerate(running_files, 1):
        try:
            with open(running_file, 'r', encoding='utf-8') as f:
                info = json.load(f)
            
            print(f"{i}. {info['project_name']}")
            print(f"   路径: {info['project_path']}")
            print(f"   开始时间: {info['start_time']}")
            if 'pid' in info:
                print(f"   进程ID: {info['pid']}")
            print()
        except Exception as e:
            print(f"读取运行文件失败: {e}")

def print_completed_details(manager, limit=10):
    """打印完成详情"""
    print(f"\n最近完成的项目 (最多{limit}个):")
    print("-" * 30)
    
    completed_files = sorted(manager.completed_dir.glob("*.json"), 
                           key=lambda x: x.stat().st_mtime, reverse=True)
    
    if not completed_files:
        print("没有已完成的项目")
        return
    
    for i, completed_file in enumerate(completed_files[:limit], 1):
        try:
            with open(completed_file, 'r', encoding='utf-8') as f:
                info = json.load(f)
            
            print(f"{i}. {info['project_name']}")
            print(f"   完成时间: {info['end_time']}")
            print(f"   返回码: {info['return_code']}")
            if info['return_code'] == 0:
                print("   状态: ✓ 成功")
            else:
                print("   状态: ✗ 失败")
            print()
        except Exception as e:
            print(f"读取完成文件失败: {e}")

def configure_system(manager):
    """配置系统"""
    print("\n当前配置:")
    print(f"AE路径: {manager.config['ae_path']}")
    print(f"最大线程数: {manager.config['max_threads']}")
    print(f"检查间隔: {manager.config['check_interval']}秒")
    
    print("\n修改配置 (直接回车保持当前值):")
    
    # AE路径
    new_ae_path = input(f"AE路径 [{manager.config['ae_path']}]: ").strip()
    if new_ae_path:
        if Path(new_ae_path).exists():
            manager.config['ae_path'] = new_ae_path
        else:
            print("警告: 指定的AE路径不存在")
    
    # 最大线程数
    new_threads = input(f"最大线程数 [{manager.config['max_threads']}]: ").strip()
    if new_threads:
        try:
            threads = int(new_threads)
            if 1 <= threads <= 16:
                manager.config['max_threads'] = threads
            else:
                print("警告: 线程数应在1-16之间")
        except ValueError:
            print("警告: 线程数必须是数字")
    
    # 检查间隔
    new_interval = input(f"检查间隔(秒) [{manager.config['check_interval']}]: ").strip()
    if new_interval:
        try:
            interval = int(new_interval)
            if interval > 0:
                manager.config['check_interval'] = interval
            else:
                print("警告: 检查间隔必须大于0")
        except ValueError:
            print("警告: 检查间隔必须是数字")
    
    # 保存配置
    if manager.save_config():
        print("✓ 配置已保存")
    else:
        print("✗ 配置保存失败")

def clear_data(manager, data_type):
    """清理数据"""
    if data_type == "completed":
        files = list(manager.completed_dir.glob("*.json"))
        type_name = "完成记录"
    elif data_type == "queue":
        files = list(manager.queue_dir.glob("*.json"))
        type_name = "队列"
    elif data_type == "logs":
        files = list(manager.logs_dir.glob("*.log"))
        type_name = "日志"
    else:
        print("无效的数据类型")
        return
    
    if not files:
        print(f"没有{type_name}需要清理")
        return
    
    print(f"找到 {len(files)} 个{type_name}文件")
    confirm = input(f"确定要删除所有{type_name}吗? (y/N): ").strip().lower()
    
    if confirm == 'y':
        deleted = 0
        for file in files:
            try:
                file.unlink()
                deleted += 1
            except Exception as e:
                print(f"删除失败: {file}, {e}")
        
        print(f"✓ 已删除 {deleted} 个{type_name}文件")
    else:
        print("取消删除操作")

def main():
    parser = argparse.ArgumentParser(description="AE多线程渲染系统命令行工具")
    parser.add_argument("command", nargs="?", choices=[
        "add", "start", "status", "queue", "running", "completed", 
        "config", "clear", "gui"
    ], help="命令")
    parser.add_argument("files", nargs="*", help="AE项目文件路径")
    parser.add_argument("--type", choices=["completed", "queue", "logs"], 
                       help="清理数据类型")
    parser.add_argument("--limit", type=int, default=10, 
                       help="显示完成项目的数量限制")
    
    args = parser.parse_args()
    
    # 创建管理器
    manager = AERenderManager()
    
    if not args.command:
        # 交互模式
        while True:
            print("\n" + "="*50)
            print("AE多线程渲染系统")
            print("="*50)
            print("1. 添加项目到队列")
            print("2. 启动渲染管理器")
            print("3. 查看状态")
            print("4. 查看队列详情")
            print("5. 查看运行详情")
            print("6. 查看完成详情")
            print("7. 配置系统")
            print("8. 清理数据")
            print("9. 启动图形界面")
            print("0. 退出")
            
            choice = input("\n请选择操作 (0-9): ").strip()
            
            if choice == "0":
                break
            elif choice == "1":
                file_path = input("请输入AE项目文件路径: ").strip()
                if file_path:
                    success, message = manager.add_project_to_queue(file_path)
                    print(f"{'✓' if success else '✗'} {message}")
            elif choice == "2":
                print("启动渲染管理器...")
                try:
                    manager.start_manager()
                except KeyboardInterrupt:
                    print("\n管理器已停止")
            elif choice == "3":
                print_status(manager)
            elif choice == "4":
                print_queue_details(manager)
            elif choice == "5":
                print_running_details(manager)
            elif choice == "6":
                print_completed_details(manager)
            elif choice == "7":
                configure_system(manager)
            elif choice == "8":
                print("\n清理选项:")
                print("1. 清理完成记录")
                print("2. 清理队列")
                print("3. 清理日志")
                clear_choice = input("请选择 (1-3): ").strip()
                if clear_choice == "1":
                    clear_data(manager, "completed")
                elif clear_choice == "2":
                    clear_data(manager, "queue")
                elif clear_choice == "3":
                    clear_data(manager, "logs")
            elif choice == "9":
                print("启动图形界面...")
                try:
                    from ae_render_gui import AERenderGUI
                    app = AERenderGUI()
                    app.run()
                except ImportError:
                    print("图形界面模块不可用")
                except Exception as e:
                    print(f"启动图形界面失败: {e}")
    
    elif args.command == "add":
        if not args.files:
            print("请指定AE项目文件")
            return
        
        for file_path in args.files:
            success, message = manager.add_project_to_queue(file_path)
            print(f"{'✓' if success else '✗'} {message}")
    
    elif args.command == "start":
        print("启动渲染管理器...")
        try:
            manager.start_manager()
        except KeyboardInterrupt:
            print("\n管理器已停止")
    
    elif args.command == "status":
        print_status(manager)
    
    elif args.command == "queue":
        print_queue_details(manager)
    
    elif args.command == "running":
        print_running_details(manager)
    
    elif args.command == "completed":
        print_completed_details(manager, args.limit)
    
    elif args.command == "config":
        configure_system(manager)
    
    elif args.command == "clear":
        if not args.type:
            print("请指定清理类型: --type completed|queue|logs")
            return
        clear_data(manager, args.type)
    
    elif args.command == "gui":
        print("启动图形界面...")
        try:
            from ae_render_gui import AERenderGUI
            app = AERenderGUI()
            app.run()
        except ImportError:
            print("图形界面模块不可用")
        except Exception as e:
            print(f"启动图形界面失败: {e}")

if __name__ == "__main__":
    main()
