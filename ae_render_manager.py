#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AE多线程渲染管理器
解决AE批处理渲染的性能和重复渲染问题
"""

import os
import sys
import json
import time
import hashlib
import subprocess
import threading
import queue
from datetime import datetime
from pathlib import Path
# import psutil  # 可选依赖

class AERenderManager:
    def __init__(self, config_file="config.json"):
        self.config_file = config_file
        self.config = self.load_config()
        
        # 创建必要的目录
        self.base_dir = Path(__file__).parent
        self.queue_dir = self.base_dir / "queue"
        self.logs_dir = self.base_dir / "logs"
        self.completed_dir = self.base_dir / "completed"
        self.running_dir = self.base_dir / "running"
        
        for dir_path in [self.queue_dir, self.logs_dir, self.completed_dir, self.running_dir]:
            dir_path.mkdir(exist_ok=True)
        
        # 渲染队列和状态
        self.render_queue = queue.Queue()
        self.running_processes = {}
        self.completed_projects = set()
        self.is_running = False
        
        # 线程锁
        self.lock = threading.Lock()
        
    def load_config(self):
        """加载配置文件"""
        default_config = {
            "ae_path": "D:/soft/AE2023/Adobe After Effects 2023/Support Files/aerender.exe",
            "max_threads": 4,
            "check_interval": 5,
            "log_level": "INFO"
        }
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # 合并默认配置
                for key, value in default_config.items():
                    if key not in config:
                        config[key] = value
                return config
            except Exception as e:
                print(f"配置文件读取失败: {e}")
                return default_config
        else:
            self.save_config(default_config)
            return default_config
    
    def save_config(self, config=None):
        """保存配置文件"""
        if config is None:
            config = self.config
        
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"配置文件保存失败: {e}")
            return False
    
    def get_project_hash(self, project_path):
        """生成项目文件的唯一标识"""
        project_path = Path(project_path)
        if not project_path.exists():
            return None
        
        # 使用文件名、大小和修改时间生成哈希
        stat = project_path.stat()
        hash_string = f"{project_path.name}_{stat.st_size}_{stat.st_mtime}"
        return hashlib.md5(hash_string.encode()).hexdigest()[:16]
    
    def is_project_completed(self, project_hash):
        """检查项目是否已完成"""
        completed_file = self.completed_dir / f"{project_hash}.json"
        return completed_file.exists()
    
    def is_project_in_queue(self, project_hash):
        """检查项目是否在队列中"""
        queue_file = self.queue_dir / f"{project_hash}.json"
        return queue_file.exists()
    
    def is_project_running(self, project_hash):
        """检查项目是否正在运行"""
        running_file = self.running_dir / f"{project_hash}.json"
        return running_file.exists()
    
    def add_project_to_queue(self, project_path):
        """添加项目到渲染队列"""
        project_path = Path(project_path)
        if not project_path.exists():
            return False, "项目文件不存在"
        
        project_hash = self.get_project_hash(project_path)
        if not project_hash:
            return False, "无法生成项目标识"
        
        # 检查各种状态
        if self.is_project_completed(project_hash):
            return False, f"项目已完成渲染: {project_path.name}"
        
        if self.is_project_in_queue(project_hash):
            return False, f"项目已在队列中: {project_path.name}"
        
        if self.is_project_running(project_hash):
            return False, f"项目正在渲染: {project_path.name}"
        
        # 添加到队列
        queue_info = {
            "project_path": str(project_path.absolute()),
            "project_name": project_path.name,
            "project_hash": project_hash,
            "added_time": datetime.now().isoformat(),
            "status": "queued"
        }
        
        queue_file = self.queue_dir / f"{project_hash}.json"
        try:
            with open(queue_file, 'w', encoding='utf-8') as f:
                json.dump(queue_info, f, indent=2, ensure_ascii=False)
            
            # 添加到内存队列
            self.render_queue.put(queue_info)
            return True, f"项目已添加到队列: {project_path.name}"
        except Exception as e:
            return False, f"添加队列失败: {e}"
    
    def start_render_process(self, queue_info):
        """启动单个渲染进程"""
        project_hash = queue_info["project_hash"]
        project_path = queue_info["project_path"]
        
        # 移动到运行状态
        queue_file = self.queue_dir / f"{project_hash}.json"
        running_file = self.running_dir / f"{project_hash}.json"
        
        running_info = queue_info.copy()
        running_info["status"] = "running"
        running_info["start_time"] = datetime.now().isoformat()
        
        try:
            # 保存运行状态
            with open(running_file, 'w', encoding='utf-8') as f:
                json.dump(running_info, f, indent=2, ensure_ascii=False)
            
            # 删除队列文件
            if queue_file.exists():
                queue_file.unlink()
            
            # 创建日志文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            log_file = self.logs_dir / f"{project_hash}_{timestamp}.log"
            
            # 启动AE渲染进程
            cmd = [self.config["ae_path"], "-project", project_path]
            
            with open(log_file, 'w', encoding='utf-8') as log_f:
                process = subprocess.Popen(
                    cmd,
                    stdout=log_f,
                    stderr=subprocess.STDOUT,
                    text=True,
                    encoding='utf-8'
                )
            
            # 记录进程信息
            running_info["pid"] = process.pid
            running_info["log_file"] = str(log_file)
            
            with open(running_file, 'w', encoding='utf-8') as f:
                json.dump(running_info, f, indent=2, ensure_ascii=False)
            
            with self.lock:
                self.running_processes[project_hash] = {
                    "process": process,
                    "info": running_info
                }
            
            return True, process
            
        except Exception as e:
            # 清理失败的启动
            if running_file.exists():
                running_file.unlink()
            return False, str(e)
    
    def check_running_processes(self):
        """检查运行中的进程状态"""
        completed_hashes = []
        
        with self.lock:
            for project_hash, proc_info in self.running_processes.items():
                process = proc_info["process"]
                info = proc_info["info"]
                
                # 检查进程是否结束
                if process.poll() is not None:
                    completed_hashes.append(project_hash)
                    
                    # 记录完成状态
                    completion_info = info.copy()
                    completion_info["status"] = "completed"
                    completion_info["end_time"] = datetime.now().isoformat()
                    completion_info["return_code"] = process.returncode
                    
                    # 保存完成记录
                    completed_file = self.completed_dir / f"{project_hash}.json"
                    with open(completed_file, 'w', encoding='utf-8') as f:
                        json.dump(completion_info, f, indent=2, ensure_ascii=False)
                    
                    # 清理运行状态文件
                    running_file = self.running_dir / f"{project_hash}.json"
                    if running_file.exists():
                        running_file.unlink()
                    
                    print(f"渲染完成: {info['project_name']} (返回码: {process.returncode})")
        
        # 从运行列表中移除已完成的进程
        for project_hash in completed_hashes:
            with self.lock:
                if project_hash in self.running_processes:
                    del self.running_processes[project_hash]
    
    def get_status(self):
        """获取当前状态"""
        queue_count = len(list(self.queue_dir.glob("*.json")))
        running_count = len(self.running_processes)
        completed_count = len(list(self.completed_dir.glob("*.json")))
        
        return {
            "queue_count": queue_count,
            "running_count": running_count,
            "completed_count": completed_count,
            "max_threads": self.config["max_threads"],
            "is_running": self.is_running
        }
    
    def start_manager(self):
        """启动渲染管理器"""
        self.is_running = True
        print("AE渲染管理器已启动")
        
        # 加载队列中的项目
        for queue_file in self.queue_dir.glob("*.json"):
            try:
                with open(queue_file, 'r', encoding='utf-8') as f:
                    queue_info = json.load(f)
                self.render_queue.put(queue_info)
            except Exception as e:
                print(f"加载队列文件失败: {queue_file}, {e}")
        
        while self.is_running:
            try:
                # 检查运行中的进程
                self.check_running_processes()
                
                # 启动新的渲染任务
                current_running = len(self.running_processes)
                max_threads = self.config["max_threads"]
                
                while current_running < max_threads and not self.render_queue.empty():
                    try:
                        queue_info = self.render_queue.get_nowait()
                        success, result = self.start_render_process(queue_info)
                        
                        if success:
                            current_running += 1
                            print(f"启动渲染: {queue_info['project_name']}")
                        else:
                            print(f"启动失败: {queue_info['project_name']}, {result}")
                            
                    except queue.Empty:
                        break
                
                # 等待一段时间再检查
                time.sleep(self.config["check_interval"])
                
            except KeyboardInterrupt:
                print("\n正在停止渲染管理器...")
                self.stop_manager()
                break
            except Exception as e:
                print(f"管理器运行错误: {e}")
                time.sleep(5)
    
    def stop_manager(self):
        """停止渲染管理器"""
        self.is_running = False
        
        # 等待所有进程完成或强制终止
        with self.lock:
            for project_hash, proc_info in self.running_processes.items():
                process = proc_info["process"]
                try:
                    process.terminate()
                    process.wait(timeout=10)
                except:
                    process.kill()
        
        print("渲染管理器已停止")


if __name__ == "__main__":
    manager = AERenderManager()
    
    if len(sys.argv) > 1:
        # 命令行模式：添加项目到队列
        project_path = sys.argv[1]
        success, message = manager.add_project_to_queue(project_path)
        print(message)
        
        if success:
            # 启动管理器（如果还没有运行）
            try:
                manager.start_manager()
            except KeyboardInterrupt:
                pass
    else:
        # 直接启动管理器
        try:
            manager.start_manager()
        except KeyboardInterrupt:
            pass
