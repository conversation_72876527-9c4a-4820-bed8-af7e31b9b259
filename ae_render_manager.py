#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AE多线程渲染管理器 (改进版)
解决AE批处理渲染的性能和重复渲染问题
增强进程管理和自动CPU检测功能
"""

import os
import sys
import json
import time
import hashlib
import subprocess
import threading
import queue
import signal
import atexit
from datetime import datetime
from pathlib import Path

class AERenderManager:
    def __init__(self, config_file="config.json"):
        self.config_file = config_file
        self.config = self.load_config()

        # 创建必要的目录
        self.base_dir = Path(__file__).parent
        self.queue_dir = self.base_dir / "queue"
        self.logs_dir = self.base_dir / "logs"
        self.completed_dir = self.base_dir / "completed"
        self.running_dir = self.base_dir / "running"

        for dir_path in [self.queue_dir, self.logs_dir, self.completed_dir, self.running_dir]:
            dir_path.mkdir(exist_ok=True)

        # 渲染队列和状态
        self.render_queue = queue.Queue()
        self.running_processes = {}
        self.completed_projects = set()
        self.is_running = False

        # 线程锁
        self.lock = threading.Lock()

        # 注册退出处理函数
        atexit.register(self.cleanup_on_exit)
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)

    def get_cpu_count(self):
        """获取CPU核心数"""
        try:
            import multiprocessing
            return multiprocessing.cpu_count()
        except:
            try:
                return os.cpu_count()
            except:
                return 4  # 默认值

    def get_recommended_threads(self):
        """获取推荐的线程数"""
        cpu_count = self.get_cpu_count()
        # 推荐使用CPU核心数的75%，但不超过8个
        recommended = max(1, min(int(cpu_count * 0.75), 8))
        return recommended

    def load_config(self):
        """加载配置文件"""
        cpu_count = self.get_cpu_count()
        recommended_threads = max(1, min(int(cpu_count * 0.75), 8))

        default_config = {
            "ae_path": "D:/soft/AE2023/Adobe After Effects 2023/Support Files/aerender.exe",
            "max_threads": recommended_threads,
            "check_interval": 5,
            "log_level": "INFO",
            "auto_detect_cpu": True,
            "cpu_count": cpu_count
        }

        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                # 如果启用了自动检测CPU，更新线程数
                if config.get("auto_detect_cpu", True):
                    current_cpu = self.get_cpu_count()
                    if current_cpu != config.get("cpu_count", 0):
                        config["cpu_count"] = current_cpu
                        config["max_threads"] = max(1, min(int(current_cpu * 0.75), 8))
                        print(f"检测到CPU核心数变化，自动调整线程数为: {config['max_threads']}")

                # 合并默认配置
                for key, value in default_config.items():
                    if key not in config:
                        config[key] = value

                # 保存更新后的配置
                self.save_config(config)
                return config
            except Exception as e:
                print(f"配置文件读取失败: {e}")
                return default_config
        else:
            self.save_config(default_config)
            return default_config

    def save_config(self, config=None):
        """保存配置文件"""
        if config is None:
            config = self.config

        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"配置文件保存失败: {e}")
            return False

    def signal_handler(self, signum, frame):
        """信号处理函数"""
        print(f"\n接收到信号 {signum}，正在安全停止...")
        self.stop_manager()
        sys.exit(0)

    def cleanup_on_exit(self):
        """程序退出时的清理函数"""
        print("正在清理资源...")
        self.terminate_all_processes()

    def terminate_all_processes(self):
        """终止所有渲染进程"""
        print("正在终止所有渲染进程...")

        with self.lock:
            for project_hash, proc_info in self.running_processes.items():
                process = proc_info["process"]
                try:
                    print(f"终止进程: {project_hash} (PID: {process.pid})")

                    # 首先尝试优雅终止
                    process.terminate()

                    # 等待进程结束
                    try:
                        process.wait(timeout=5)
                        print(f"进程 {process.pid} 已优雅终止")
                    except subprocess.TimeoutExpired:
                        # 如果5秒内没有结束，强制杀死
                        print(f"强制杀死进程 {process.pid}")
                        process.kill()
                        process.wait()

                    # 清理运行状态文件
                    running_file = self.running_dir / f"{project_hash}.json"
                    if running_file.exists():
                        running_file.unlink()

                except Exception as e:
                    print(f"终止进程失败: {e}")
                    # 尝试使用系统命令强制杀死进程
                    try:
                        if os.name == 'nt':  # Windows
                            os.system(f"taskkill /F /PID {process.pid}")
                        else:  # Unix/Linux
                            os.system(f"kill -9 {process.pid}")
                    except:
                        pass

        # 清空进程列表
        self.running_processes.clear()
        print("所有进程已终止")

    def get_project_hash(self, project_path):
        """生成项目文件的唯一标识"""
        project_path = Path(project_path)
        if not project_path.exists():
            return None

        # 使用文件名、大小和修改时间生成哈希
        stat = project_path.stat()
        hash_string = f"{project_path.name}_{stat.st_size}_{stat.st_mtime}"
        return hashlib.md5(hash_string.encode()).hexdigest()[:16]

    def is_project_completed(self, project_hash):
        """检查项目是否已完成"""
        completed_file = self.completed_dir / f"{project_hash}.json"
        return completed_file.exists()

    def is_project_in_queue(self, project_hash):
        """检查项目是否在队列中"""
        queue_file = self.queue_dir / f"{project_hash}.json"
        return queue_file.exists()

    def is_project_running(self, project_hash):
        """检查项目是否正在运行"""
        running_file = self.running_dir / f"{project_hash}.json"
        return running_file.exists()

    def add_project_to_queue(self, project_path):
        """添加项目到渲染队列"""
        project_path = Path(project_path)
        if not project_path.exists():
            return False, "项目文件不存在"

        project_hash = self.get_project_hash(project_path)
        if not project_hash:
            return False, "无法生成项目标识"

        # 检查各种状态
        if self.is_project_completed(project_hash):
            return False, f"项目已完成渲染: {project_path.name}"

        if self.is_project_in_queue(project_hash):
            return False, f"项目已在队列中: {project_path.name}"

        if self.is_project_running(project_hash):
            return False, f"项目正在渲染: {project_path.name}"

        # 添加到队列
        queue_info = {
            "project_path": str(project_path.absolute()),
            "project_name": project_path.name,
            "project_hash": project_hash,
            "added_time": datetime.now().isoformat(),
            "status": "queued"
        }

        queue_file = self.queue_dir / f"{project_hash}.json"
        try:
            with open(queue_file, 'w', encoding='utf-8') as f:
                json.dump(queue_info, f, indent=2, ensure_ascii=False)

            # 添加到内存队列
            self.render_queue.put(queue_info)
            return True, f"项目已添加到队列: {project_path.name}"
        except Exception as e:
            return False, f"添加队列失败: {e}"

    def start_render_process(self, queue_info):
        """启动单个渲染进程"""
        project_hash = queue_info["project_hash"]
        project_path = queue_info["project_path"]

        # 移动到运行状态
        queue_file = self.queue_dir / f"{project_hash}.json"
        running_file = self.running_dir / f"{project_hash}.json"

        running_info = queue_info.copy()
        running_info["status"] = "running"
        running_info["start_time"] = datetime.now().isoformat()

        try:
            # 保存运行状态
            with open(running_file, 'w', encoding='utf-8') as f:
                json.dump(running_info, f, indent=2, ensure_ascii=False)

            # 删除队列文件
            if queue_file.exists():
                queue_file.unlink()

            # 创建日志文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            log_file = self.logs_dir / f"{project_hash}_{timestamp}.log"

            # 启动AE渲染进程
            cmd = [self.config["ae_path"], "-project", project_path]

            print(f"启动渲染命令: {' '.join(cmd)}")

            with open(log_file, 'w', encoding='utf-8') as log_f:
                # 写入命令信息到日志
                log_f.write(f"渲染命令: {' '.join(cmd)}\n")
                log_f.write(f"开始时间: {datetime.now().isoformat()}\n")
                log_f.write("-" * 50 + "\n")
                log_f.flush()

                process = subprocess.Popen(
                    cmd,
                    stdout=log_f,
                    stderr=subprocess.STDOUT,
                    text=True,
                    encoding='utf-8',
                    creationflags=subprocess.CREATE_NEW_PROCESS_GROUP if os.name == 'nt' else 0
                )

            # 记录进程信息
            running_info["pid"] = process.pid
            running_info["log_file"] = str(log_file)

            with open(running_file, 'w', encoding='utf-8') as f:
                json.dump(running_info, f, indent=2, ensure_ascii=False)

            with self.lock:
                self.running_processes[project_hash] = {
                    "process": process,
                    "info": running_info
                }

            return True, process

        except Exception as e:
            # 清理失败的启动
            if running_file.exists():
                running_file.unlink()
            return False, str(e)

    def check_running_processes(self):
        """检查运行中的进程状态"""
        completed_hashes = []

        with self.lock:
            for project_hash, proc_info in list(self.running_processes.items()):
                process = proc_info["process"]
                info = proc_info["info"]

                # 检查进程是否结束
                if process.poll() is not None:
                    completed_hashes.append(project_hash)

                    # 记录完成状态
                    completion_info = info.copy()
                    completion_info["status"] = "completed"
                    completion_info["end_time"] = datetime.now().isoformat()
                    completion_info["return_code"] = process.returncode

                    # 分析返回码
                    if process.returncode == 0:
                        status_msg = "成功"
                        completion_info["result"] = "success"
                    elif process.returncode == 2:
                        status_msg = "失败 - 项目中没有渲染队列或渲染队列为空"
                        completion_info["result"] = "no_render_queue"
                        completion_info["error_hint"] = "请在AE中设置渲染队列后保存项目"
                    elif process.returncode == 1:
                        status_msg = "失败 - 渲染过程中出现错误"
                        completion_info["result"] = "render_error"
                    else:
                        status_msg = f"失败 - 未知错误(返回码: {process.returncode})"
                        completion_info["result"] = "unknown_error"

                    # 检查日志文件获取更多信息
                    if "log_file" in info:
                        log_file = Path(info["log_file"])
                        if log_file.exists():
                            try:
                                with open(log_file, 'r', encoding='utf-8') as f:
                                    log_content = f.read()
                                    completion_info["log_size"] = len(log_content)

                                    # 检查常见错误模式
                                    if "No compositions to render" in log_content:
                                        completion_info["error_hint"] = "项目中没有合成添加到渲染队列"
                                    elif "Error" in log_content or "ERROR" in log_content:
                                        completion_info["has_errors"] = True
                                    elif "PROGRESS:" in log_content:
                                        completion_info["has_progress"] = True
                            except:
                                pass

                    # 保存完成记录
                    completed_file = self.completed_dir / f"{project_hash}.json"
                    with open(completed_file, 'w', encoding='utf-8') as f:
                        json.dump(completion_info, f, indent=2, ensure_ascii=False)

                    # 清理运行状态文件
                    running_file = self.running_dir / f"{project_hash}.json"
                    if running_file.exists():
                        running_file.unlink()

                    print(f"渲染完成: {info['project_name']} - {status_msg}")

                    # 如果是常见错误，给出建议
                    if process.returncode == 2:
                        print("  💡 建议: 请在AE中为项目设置渲染队列")
                        print("     1. 打开AE项目")
                        print("     2. 选择合成 → 添加到渲染队列")
                        print("     3. 设置输出格式和路径")
                        print("     4. 保存项目")

        # 从运行列表中移除已完成的进程
        for project_hash in completed_hashes:
            with self.lock:
                if project_hash in self.running_processes:
                    del self.running_processes[project_hash]

    def get_status(self):
        """获取当前状态"""
        queue_count = len(list(self.queue_dir.glob("*.json")))
        running_count = len(self.running_processes)
        completed_count = len(list(self.completed_dir.glob("*.json")))

        return {
            "queue_count": queue_count,
            "running_count": running_count,
            "completed_count": completed_count,
            "max_threads": self.config["max_threads"],
            "cpu_count": self.config.get("cpu_count", self.get_cpu_count()),
            "auto_detect_cpu": self.config.get("auto_detect_cpu", True),
            "is_running": self.is_running
        }

    def start_manager(self):
        """启动渲染管理器"""
        self.is_running = True
        print("="*50)
        print("AE渲染管理器已启动")
        print(f"CPU核心数: {self.config.get('cpu_count', self.get_cpu_count())}")
        print(f"最大线程数: {self.config['max_threads']}")
        print(f"自动检测CPU: {'是' if self.config.get('auto_detect_cpu', True) else '否'}")
        print("="*50)

        # 加载队列中的项目
        for queue_file in self.queue_dir.glob("*.json"):
            try:
                with open(queue_file, 'r', encoding='utf-8') as f:
                    queue_info = json.load(f)
                self.render_queue.put(queue_info)
            except Exception as e:
                print(f"加载队列文件失败: {queue_file}, {e}")

        try:
            while self.is_running:
                # 检查运行中的进程
                self.check_running_processes()

                # 启动新的渲染任务
                current_running = len(self.running_processes)
                max_threads = self.config["max_threads"]

                while current_running < max_threads and not self.render_queue.empty():
                    try:
                        queue_info = self.render_queue.get_nowait()
                        success, result = self.start_render_process(queue_info)

                        if success:
                            current_running += 1
                            print(f"启动渲染: {queue_info['project_name']} (PID: {result.pid})")
                        else:
                            print(f"启动失败: {queue_info['project_name']}, {result}")

                    except queue.Empty:
                        break

                # 等待一段时间再检查
                time.sleep(self.config["check_interval"])

        except KeyboardInterrupt:
            print("\n接收到中断信号，正在安全停止...")
        except Exception as e:
            print(f"管理器运行错误: {e}")
        finally:
            self.stop_manager()

    def stop_manager(self):
        """停止渲染管理器"""
        print("正在停止渲染管理器...")
        self.is_running = False

        # 终止所有运行中的进程
        self.terminate_all_processes()

        print("渲染管理器已停止")


if __name__ == "__main__":
    manager = AERenderManager()

    if len(sys.argv) > 1:
        # 命令行模式：添加项目到队列
        project_path = sys.argv[1]
        success, message = manager.add_project_to_queue(project_path)
        print(message)

        if success:
            # 启动管理器（如果还没有运行）
            try:
                manager.start_manager()
            except KeyboardInterrupt:
                pass
    else:
        # 直接启动管理器
        try:
            manager.start_manager()
        except KeyboardInterrupt:
            pass