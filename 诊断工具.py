#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AE渲染系统诊断工具
帮助排查渲染无进展的问题
"""

import os
import sys
import json
import subprocess
import time
from pathlib import Path
from ae_render_manager import AERenderManager

def print_header(title):
    """打印标题"""
    print("\n" + "="*60)
    print(f"  {title}")
    print("="*60)

def print_section(title):
    """打印小节标题"""
    print(f"\n{title}")
    print("-" * len(title))

def check_ae_installation():
    """检查AE安装和配置"""
    print_header("AE安装和配置检查")
    
    manager = AERenderManager()
    ae_path = manager.config["ae_path"]
    
    print(f"配置的AE路径: {ae_path}")
    
    # 检查文件是否存在
    if not Path(ae_path).exists():
        print("❌ AE路径不存在！")
        print("请检查AE是否正确安装，或更新配置中的路径")
        return False
    else:
        print("✅ AE路径存在")
    
    # 检查文件是否可执行
    if not os.access(ae_path, os.X_OK):
        print("❌ AE文件不可执行！")
        return False
    else:
        print("✅ AE文件可执行")
    
    # 测试AE命令行
    print_section("测试AE命令行")
    try:
        print("正在测试AE命令行响应...")
        result = subprocess.run(
            [ae_path, "-help"],
            capture_output=True,
            text=True,
            timeout=30,
            encoding='utf-8'
        )
        
        if result.returncode == 0:
            print("✅ AE命令行响应正常")
            print("AE版本信息:")
            print(result.stdout[:200] + "..." if len(result.stdout) > 200 else result.stdout)
        else:
            print(f"❌ AE命令行返回错误码: {result.returncode}")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ AE命令行响应超时")
        return False
    except Exception as e:
        print(f"❌ AE命令行测试失败: {e}")
        return False
    
    return True

def check_project_files():
    """检查项目文件"""
    print_header("项目文件检查")
    
    manager = AERenderManager()
    
    # 检查队列中的项目
    queue_files = list(manager.queue_dir.glob("*.json"))
    running_files = list(manager.running_dir.glob("*.json"))
    
    print(f"队列中项目数: {len(queue_files)}")
    print(f"运行中项目数: {len(running_files)}")
    
    if not queue_files and not running_files:
        print("❌ 没有项目在队列中或运行中")
        print("请先添加AE项目到渲染队列")
        return False
    
    # 检查运行中的项目
    for running_file in running_files:
        print_section(f"检查运行中项目: {running_file.name}")
        
        try:
            with open(running_file, 'r', encoding='utf-8') as f:
                info = json.load(f)
            
            project_path = info["project_path"]
            print(f"项目路径: {project_path}")
            
            # 检查项目文件是否存在
            if not Path(project_path).exists():
                print("❌ 项目文件不存在！")
                continue
            else:
                print("✅ 项目文件存在")
            
            # 检查项目文件大小
            file_size = Path(project_path).stat().st_size
            print(f"项目文件大小: {file_size} 字节")
            
            # 检查进程是否还在运行
            if "pid" in info:
                pid = info["pid"]
                print(f"进程ID: {pid}")
                
                try:
                    if sys.platform == "win32":
                        result = subprocess.run(
                            ["tasklist", "/FI", f"PID eq {pid}"],
                            capture_output=True, text=True
                        )
                        if str(pid) in result.stdout:
                            print("✅ 进程仍在运行")
                        else:
                            print("❌ 进程已结束")
                    else:
                        result = subprocess.run(
                            ["ps", "-p", str(pid)],
                            capture_output=True, text=True
                        )
                        if result.returncode == 0:
                            print("✅ 进程仍在运行")
                        else:
                            print("❌ 进程已结束")
                except Exception as e:
                    print(f"检查进程状态失败: {e}")
            
            # 检查日志文件
            if "log_file" in info:
                log_file = Path(info["log_file"])
                print(f"日志文件: {log_file}")
                
                if log_file.exists():
                    log_size = log_file.stat().st_size
                    print(f"日志文件大小: {log_size} 字节")
                    
                    if log_size > 0:
                        print("最近的日志内容:")
                        try:
                            with open(log_file, 'r', encoding='utf-8') as f:
                                lines = f.readlines()
                                for line in lines[-10:]:  # 显示最后10行
                                    print(f"  {line.strip()}")
                        except Exception as e:
                            print(f"读取日志失败: {e}")
                    else:
                        print("❌ 日志文件为空，可能渲染未开始")
                else:
                    print("❌ 日志文件不存在")
                    
        except Exception as e:
            print(f"检查项目文件失败: {e}")
    
    return True

def test_manual_render():
    """测试手动渲染"""
    print_header("手动渲染测试")
    
    manager = AERenderManager()
    ae_path = manager.config["ae_path"]
    
    # 查找测试项目
    test_projects = list(Path(".").glob("*.aep"))
    if not test_projects:
        print("❌ 未找到AE项目文件进行测试")
        return False
    
    test_project = test_projects[0]
    print(f"使用测试项目: {test_project}")
    
    # 手动执行AE渲染命令
    cmd = [ae_path, "-project", str(test_project.absolute())]
    print(f"执行命令: {' '.join(cmd)}")
    
    print("\n开始手动渲染测试 (10秒后自动终止)...")
    try:
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            encoding='utf-8'
        )
        
        # 等待10秒
        try:
            stdout, stderr = process.communicate(timeout=10)
            print("渲染输出:")
            print(stdout)
            if stderr:
                print("错误输出:")
                print(stderr)
        except subprocess.TimeoutExpired:
            print("10秒后终止进程...")
            process.terminate()
            try:
                stdout, stderr = process.communicate(timeout=5)
                print("渲染输出:")
                print(stdout)
            except subprocess.TimeoutExpired:
                process.kill()
                print("强制终止进程")
        
        return True
        
    except Exception as e:
        print(f"手动渲染测试失败: {e}")
        return False

def check_system_resources():
    """检查系统资源"""
    print_header("系统资源检查")
    
    try:
        import psutil
        
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        print(f"CPU使用率: {cpu_percent}%")
        
        # 内存使用率
        memory = psutil.virtual_memory()
        print(f"内存使用率: {memory.percent}%")
        print(f"可用内存: {memory.available / 1024 / 1024 / 1024:.1f} GB")
        
        # 磁盘空间
        disk = psutil.disk_usage('.')
        print(f"磁盘使用率: {disk.percent}%")
        print(f"可用磁盘空间: {disk.free / 1024 / 1024 / 1024:.1f} GB")
        
        # 检查AE进程
        ae_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if 'aerender' in proc.info['name'].lower():
                    ae_processes.append(proc.info)
            except:
                pass
        
        print(f"\n当前AE渲染进程数: {len(ae_processes)}")
        for proc in ae_processes:
            print(f"  PID: {proc['pid']}, 命令: {' '.join(proc['cmdline'][:3])}")
            
    except ImportError:
        print("psutil未安装，跳过系统资源检查")
        print("可以运行: pip install psutil 来安装")

def main():
    """主诊断函数"""
    print_header("AE渲染系统诊断工具")
    print("此工具将帮助诊断渲染无进展的问题")
    
    # 执行所有检查
    checks = [
        ("AE安装和配置", check_ae_installation),
        ("项目文件", check_project_files),
        ("系统资源", check_system_resources),
        ("手动渲染测试", test_manual_render)
    ]
    
    results = {}
    
    for check_name, check_func in checks:
        print(f"\n正在执行: {check_name}")
        try:
            results[check_name] = check_func()
        except Exception as e:
            print(f"检查失败: {e}")
            results[check_name] = False
    
    # 总结
    print_header("诊断结果总结")
    
    for check_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{check_name}: {status}")
    
    # 建议
    print_section("建议")
    
    if not results.get("AE安装和配置", False):
        print("1. 检查AE安装路径是否正确")
        print("2. 确认AE版本支持命令行渲染")
        print("3. 尝试重新安装AE或修复安装")
    
    if not results.get("项目文件", False):
        print("1. 确保AE项目文件存在且未损坏")
        print("2. 检查项目中的渲染队列设置")
        print("3. 确认项目保存时包含了渲染设置")
    
    print("\n如果问题仍然存在，请提供诊断结果进行进一步分析。")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"诊断工具运行失败: {e}")
    finally:
        input("\n按回车键退出...")
