#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AE项目渲染队列检查工具
快速检查项目是否设置了渲染队列
"""

import subprocess
import sys
from pathlib import Path
from ae_render_manager import AERenderManager

def check_ae_project(project_path):
    """检查AE项目是否设置了渲染队列"""
    print("="*60)
    print("AE项目渲染队列检查")
    print("="*60)
    
    project_path = Path(project_path)
    if not project_path.exists():
        print(f"❌ 项目文件不存在: {project_path}")
        return False
    
    print(f"检查项目: {project_path.name}")
    print(f"文件大小: {project_path.stat().st_size / 1024 / 1024:.1f} MB")
    
    # 获取AE路径
    manager = AERenderManager()
    ae_path = manager.config["ae_path"]
    
    if not Path(ae_path).exists():
        print(f"❌ AE路径不存在: {ae_path}")
        print("请先配置正确的AE路径")
        return False
    
    print(f"使用AE: {ae_path}")
    print()
    
    # 测试渲染命令
    cmd = [ae_path, "-project", str(project_path.absolute())]
    print("执行命令:")
    print(" ".join(cmd))
    print()
    
    print("开始检查...")
    print("-" * 40)
    
    try:
        # 运行AE命令并捕获输出
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=30,
            encoding='utf-8'
        )
        
        print("AE输出:")
        if result.stdout:
            print(result.stdout)
        
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        
        print(f"\n返回码: {result.returncode}")
        
        # 分析结果
        print("\n" + "="*40)
        print("分析结果:")
        print("="*40)
        
        if result.returncode == 0:
            print("✅ 项目检查通过！")
            print("   项目中有正确设置的渲染队列")
            print("   可以使用批处理渲染系统")
            return True
            
        elif result.returncode == 2:
            print("❌ 项目中没有渲染队列！")
            print("\n解决方案:")
            print("1. 在AE中打开此项目")
            print("2. 选择要渲染的合成")
            print("3. 菜单: 合成 → 添加到渲染队列 (Ctrl+M)")
            print("4. 设置输出格式和路径")
            print("5. 保存项目 (Ctrl+S)")
            print("\n详细设置指南请查看: AE项目设置指南.md")
            return False
            
        elif result.returncode == 1:
            print("❌ 渲染过程中出现错误")
            print("可能的原因:")
            print("- 项目文件损坏")
            print("- 缺少素材文件")
            print("- 输出路径无效")
            print("- AE版本不兼容")
            return False
            
        else:
            print(f"❌ 未知错误 (返回码: {result.returncode})")
            print("请检查:")
            print("- AE安装是否正常")
            print("- 项目文件是否完整")
            print("- 系统资源是否充足")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 检查超时 (30秒)")
        print("可能的原因:")
        print("- AE启动缓慢")
        print("- 项目文件过大")
        print("- 系统资源不足")
        return False
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def main():
    """主函数"""
    if len(sys.argv) > 1:
        # 命令行参数
        project_path = sys.argv[1]
        check_ae_project(project_path)
    else:
        # 交互模式
        print("AE项目渲染队列检查工具")
        print("="*30)
        
        # 查找当前目录的AE项目
        current_dir = Path(".")
        ae_projects = list(current_dir.glob("*.aep"))
        
        if ae_projects:
            print(f"\n找到 {len(ae_projects)} 个AE项目:")
            for i, project in enumerate(ae_projects, 1):
                print(f"  {i}. {project.name}")
            
            print(f"  {len(ae_projects) + 1}. 手动输入路径")
            print("  0. 退出")
            
            try:
                choice = int(input(f"\n请选择项目 (0-{len(ae_projects) + 1}): "))
                
                if choice == 0:
                    return
                elif 1 <= choice <= len(ae_projects):
                    project_path = ae_projects[choice - 1]
                    check_ae_project(project_path)
                elif choice == len(ae_projects) + 1:
                    project_path = input("请输入AE项目文件路径: ").strip().strip('"\'')
                    if project_path:
                        check_ae_project(project_path)
                else:
                    print("无效选择")
                    
            except ValueError:
                print("请输入有效数字")
        else:
            print("\n当前目录没有找到AE项目文件(.aep)")
            project_path = input("请输入AE项目文件路径: ").strip().strip('"\'')
            if project_path:
                check_ae_project(project_path)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n检查已取消")
    except Exception as e:
        print(f"\n程序运行错误: {e}")
    finally:
        input("\n按回车键退出...")
