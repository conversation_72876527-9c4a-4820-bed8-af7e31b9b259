@echo off
setlocal enabledelayedexpansion

:: 配置参数
set "QUEUE_DIR=%~dp0queue"
set "LOG_DIR=%~dp0logs"
set "COMPLETED_DIR=%~dp0completed"
set "RUNNING_DIR=%~dp0running"

echo ========================================
echo       AE渲染状态查看器
echo ========================================
echo 时间: %date% %time%
echo.

:: 统计各种状态的项目数量
set "QUEUE_COUNT=0"
set "RUNNING_COUNT=0"
set "COMPLETED_COUNT=0"

for %%f in ("%QUEUE_DIR%\*.queue") do set /a QUEUE_COUNT+=1
for %%f in ("%RUNNING_DIR%\*.running") do set /a RUNNING_COUNT+=1
for %%f in ("%COMPLETED_DIR%\*.completed") do set /a COMPLETED_COUNT+=1

echo 总体状态:
echo   队列中: %QUEUE_COUNT% 个项目
echo   正在渲染: %RUNNING_COUNT% 个项目  
echo   已完成: %COMPLETED_COUNT% 个项目
echo.

:: 显示队列中的项目
if %QUEUE_COUNT% gtr 0 (
    echo ========================================
    echo 等待渲染的项目:
    echo ========================================
    for %%f in ("%QUEUE_DIR%\*.queue") do (
        set "queue_file=%%f"
        set "project_hash=%%~nf"
        
        :: 读取队列时间和项目路径
        set "line_num=0"
        for /f "usebackq delims=" %%a in ("!queue_file!") do (
            set /a line_num+=1
            if !line_num!==1 set "queue_time=%%a"
            if !line_num!==2 set "project_path=%%a"
        )
        
        echo 项目: !project_hash!
        echo   加入队列时间: !queue_time!
        echo   文件路径: !project_path!
        echo.
    )
) else (
    echo 当前没有项目在队列中等待
    echo.
)

:: 显示正在渲染的项目
if %RUNNING_COUNT% gtr 0 (
    echo ========================================
    echo 正在渲染的项目:
    echo ========================================
    for %%f in ("%RUNNING_DIR%\*.running") do (
        set "running_file=%%f"
        set "project_hash=%%~nf"
        set "pid_file=%RUNNING_DIR%\!project_hash!.pid"
        
        :: 读取运行信息
        set "line_num=0"
        for /f "usebackq delims=" %%a in ("!running_file!") do (
            set /a line_num+=1
            if !line_num!==1 set "start_time=%%a"
            if !line_num!==2 set "project_path=%%a"
        )
        
        echo 项目: !project_hash!
        echo   开始时间: !start_time!
        echo   文件路径: !project_path!
        
        :: 检查进程是否还在运行
        if exist "!pid_file!" (
            set /p pid=<"!pid_file!"
            tasklist /FI "PID eq !pid!" 2>NUL | find "!pid!" >NUL
            if errorlevel 1 (
                echo   状态: 进程已结束（等待清理）
            ) else (
                echo   状态: 正在运行 (PID: !pid!)
            )
        ) else (
            echo   状态: 启动中...
        )
        
        :: 显示最新日志文件大小（作为进度指示）
        for /f "delims=" %%g in ('dir /b /o-d "%LOG_DIR%\!project_hash!_*.log" 2^>NUL') do (
            set "latest_log=%LOG_DIR%\%%g"
            goto show_log_size
        )
        goto next_running
        
        :show_log_size
        for %%h in ("!latest_log!") do (
            set "log_size=%%~zh"
            if defined log_size (
                set /a log_size_kb=!log_size!/1024
                echo   日志大小: !log_size_kb! KB
            )
        )
        
        :next_running
        echo.
    )
) else (
    echo 当前没有项目正在渲染
    echo.
)

:: 显示最近完成的项目
if %COMPLETED_COUNT% gtr 0 (
    echo ========================================
    echo 最近完成的项目 (最多显示5个):
    echo ========================================
    set "shown_count=0"
    for /f "delims=" %%f in ('dir /b /o-d "%COMPLETED_DIR%\*.completed" 2^>NUL') do (
        if !shown_count! lss 5 (
            set "completed_file=%COMPLETED_DIR%\%%f"
            set "project_hash=%%~nf"
            
            echo 项目: !project_hash!
            echo   完成信息:
            for /f "usebackq delims=" %%a in ("!completed_file!") do (
                echo     %%a
            )
            echo.
            set /a shown_count+=1
        )
    )
    
    if %COMPLETED_COUNT% gtr 5 (
        set /a remaining=%COMPLETED_COUNT%-5
        echo ... 还有 !remaining! 个已完成的项目
        echo.
    )
) else (
    echo 还没有完成的项目
    echo.
)

echo ========================================
echo 操作选项:
echo   1. 刷新状态 (R)
echo   2. 查看详细日志 (L)  
echo   3. 清理已完成项目 (C)
echo   4. 退出 (Q)
echo ========================================
set /p choice=请选择操作 (R/L/C/Q): 

if /i "%choice%"=="R" goto :eof
if /i "%choice%"=="L" call :SHOW_LOGS
if /i "%choice%"=="C" call :CLEAN_COMPLETED
if /i "%choice%"=="Q" exit /b

goto :eof

:SHOW_LOGS
echo.
echo 可用的日志文件:
dir /b "%LOG_DIR%\*.log" 2>NUL
echo.
set /p log_choice=请输入要查看的日志文件名 (或按回车返回): 
if not "%log_choice%"=="" (
    if exist "%LOG_DIR%\%log_choice%" (
        echo.
        echo ========== %log_choice% ==========
        type "%LOG_DIR%\%log_choice%"
        echo.
        pause
    ) else (
        echo 文件不存在: %log_choice%
        pause
    )
)
goto :eof

:CLEAN_COMPLETED
echo.
set /p confirm=确定要清理所有已完成的项目记录吗? (Y/N): 
if /i "%confirm%"=="Y" (
    del "%COMPLETED_DIR%\*.completed" 2>NUL
    echo 已清理完成项目记录
) else (
    echo 取消清理操作
)
pause
goto :eof
