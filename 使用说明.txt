========================================
    AE多线程渲染系统 使用说明
========================================

## 系统概述

这是一个改进的After Effects批处理渲染系统，解决了原始脚本的以下问题：
- 顺序渲染导致的性能浪费
- 重复渲染覆盖已完成文件
- 无法有效利用多核CPU性能
- 缺乏渲染状态监控

## 主要特性

✓ 真正的多线程并行渲染
✓ 智能队列管理，避免重复渲染
✓ 实时状态监控和进度跟踪
✓ 详细的日志记录
✓ 可配置的参数设置
✓ 自动进程管理和清理

## 文件说明

1. 渲染.bat - 主入口脚本，用于添加项目到渲染队列
2. 渲染管理器.bat - 核心管理器，负责多线程渲染调度
3. 查看渲染状态.bat - 状态监控工具
4. 配置.bat - 参数配置工具
5. 使用说明.txt - 本文档

## 使用步骤

### 首次使用配置

1. 运行"配置.bat"
2. 检查并修改AE路径（如果不是默认安装路径）
3. 根据你的CPU核心数设置合适的最大线程数
4. 保存配置

### 开始渲染

1. 在AE中设置好项目的渲染队列和输出设置
2. 保存AE项目文件
3. 将AE项目文件拖放到"渲染.bat"上
4. 系统会自动：
   - 检查是否已在队列中
   - 添加到渲染队列
   - 启动渲染管理器（如果未运行）

### 监控渲染

- 运行"查看渲染状态.bat"查看详细状态
- 渲染管理器窗口会显示实时状态
- 日志文件保存在logs目录中

## 目录结构

AE批处理渲染/
├── 渲染.bat                 # 主入口
├── 渲染管理器.bat            # 核心管理器
├── 查看渲染状态.bat          # 状态查看器
├── 配置.bat                 # 配置工具
├── 使用说明.txt             # 本文档
├── config.ini               # 配置文件（自动生成）
├── queue/                   # 队列目录
├── running/                 # 运行状态目录
├── completed/               # 完成记录目录
└── logs/                    # 日志目录

## 配置建议

### 最大线程数设置

- 4核CPU: 建议2-3线程
- 6-8核CPU: 建议3-5线程
- 8核以上: 建议4-8线程

注意：线程数过多可能导致：
- 系统响应变慢
- 内存不足
- 渲染质量下降

### AE项目准备

1. 确保项目中的素材路径正确
2. 设置好渲染队列的输出格式和路径
3. 建议使用序列输出而非单个视频文件
4. 保存项目后再拖放到渲染脚本

## 常见问题

Q: 为什么拖放多个文件还是顺序渲染？
A: 新系统会将所有文件加入队列，然后由管理器根据设定的线程数并行处理。

Q: 如何避免重复渲染？
A: 系统会自动检查文件是否已在队列或已完成，重复拖放不会重复添加。

Q: 渲染失败怎么办？
A: 查看logs目录中的日志文件，或使用状态查看器检查错误信息。

Q: 如何重新渲染已完成的项目？
A: 删除completed目录中对应的.completed文件，然后重新拖放项目文件。

Q: 渲染管理器意外关闭怎么办？
A: 重新拖放任意项目文件，系统会自动重启管理器。

## 性能优化建议

1. 关闭不必要的后台程序
2. 确保有足够的磁盘空间
3. 使用SSD存储输出文件
4. 根据项目复杂度调整线程数
5. 定期清理日志和完成记录

## 故障排除

如果遇到问题：
1. 检查AE路径是否正确
2. 确认项目文件没有损坏
3. 查看最新的日志文件
4. 重启渲染管理器
5. 检查系统资源使用情况

## 版本信息

版本: 2.0
更新日期: 2025-07-02
主要改进: 多线程支持、队列管理、状态监控

========================================
