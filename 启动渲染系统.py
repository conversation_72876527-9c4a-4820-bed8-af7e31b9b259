#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AE多线程渲染系统 - 主启动脚本
"""

import sys
import os
import subprocess
from pathlib import Path

def check_python():
    """检查Python版本"""
    if sys.version_info < (3, 6):
        print("错误: 需要Python 3.6或更高版本")
        return False
    return True

def check_dependencies():
    """检查依赖"""
    try:
        import tkinter
        return True
    except ImportError:
        print("警告: tkinter不可用，图形界面将无法使用")
        return False

def main():
    print("="*60)
    print("AE多线程渲染系统")
    print("="*60)
    print("版本: 2.0 (Python版)")
    print("作者: AI Assistant")
    print("="*60)
    
    # 检查环境
    if not check_python():
        input("按回车键退出...")
        return
    
    has_gui = check_dependencies()
    
    print("\n系统组件:")
    print("✓ 核心管理器 (ae_render_manager.py)")
    print("✓ 命令行工具 (ae_render_cli.py)")
    print("✓ 拖放工具 (拖放添加项目.py)")
    if has_gui:
        print("✓ 图形界面 (ae_render_gui.py)")
    else:
        print("✗ 图形界面 (tkinter不可用)")
    
    print("\n启动选项:")
    print("1. 启动图形界面 (推荐)")
    print("2. 启动命令行工具")
    print("3. 直接启动渲染管理器")
    print("4. 配置系统")
    print("5. 查看使用说明")
    print("6. 退出")
    
    while True:
        choice = input("\n请选择启动方式 (1-6): ").strip()
        
        if choice == "1":
            if has_gui:
                print("\n启动图形界面...")
                try:
                    from ae_render_gui import AERenderGUI
                    app = AERenderGUI()
                    app.run()
                    break
                except Exception as e:
                    print(f"启动图形界面失败: {e}")
            else:
                print("图形界面不可用")
        
        elif choice == "2":
            print("\n启动命令行工具...")
            try:
                from ae_render_cli import main as cli_main
                cli_main()
                break
            except Exception as e:
                print(f"启动命令行工具失败: {e}")
        
        elif choice == "3":
            print("\n启动渲染管理器...")
            print("按 Ctrl+C 停止管理器")
            try:
                from ae_render_manager import AERenderManager
                manager = AERenderManager()
                manager.start_manager()
                break
            except KeyboardInterrupt:
                print("\n管理器已停止")
                break
            except Exception as e:
                print(f"启动管理器失败: {e}")
        
        elif choice == "4":
            print("\n配置系统...")
            try:
                from ae_render_manager import AERenderManager
                manager = AERenderManager()
                
                print(f"\n当前配置:")
                print(f"AE路径: {manager.config['ae_path']}")
                print(f"最大线程数: {manager.config['max_threads']}")
                print(f"检查间隔: {manager.config['check_interval']}秒")
                
                # 检查AE路径
                ae_path = Path(manager.config['ae_path'])
                if ae_path.exists():
                    print("✓ AE路径有效")
                else:
                    print("✗ AE路径无效")
                    new_path = input("请输入正确的AE路径: ").strip()
                    if new_path and Path(new_path).exists():
                        manager.config['ae_path'] = new_path
                        manager.save_config()
                        print("✓ AE路径已更新")
                
                # 线程数建议
                try:
                    import psutil
                    cpu_count = psutil.cpu_count()
                    print(f"\n系统信息:")
                    print(f"CPU核心数: {cpu_count}")
                    print(f"建议线程数: {min(cpu_count, 8)}")
                except ImportError:
                    print(f"\n建议线程数: 4 (无法检测CPU核心数)")
                except:
                    print(f"\n建议线程数: 4")
                
                new_threads = input(f"设置线程数 (当前: {manager.config['max_threads']}): ").strip()
                if new_threads:
                    try:
                        threads = int(new_threads)
                        if 1 <= threads <= 16:
                            manager.config['max_threads'] = threads
                            manager.save_config()
                            print("✓ 线程数已更新")
                        else:
                            print("线程数应在1-16之间")
                    except ValueError:
                        print("无效的线程数")
                
            except Exception as e:
                print(f"配置失败: {e}")
        
        elif choice == "5":
            show_usage()
        
        elif choice == "6":
            break
        
        else:
            print("无效选择，请重新输入")

def show_usage():
    """显示使用说明"""
    print("\n" + "="*60)
    print("使用说明")
    print("="*60)
    
    print("\n1. 快速开始:")
    print("   - 双击 '启动渲染系统.py' 启动主程序")
    print("   - 选择启动图形界面 (推荐新手)")
    print("   - 或选择命令行工具 (适合高级用户)")
    
    print("\n2. 添加项目到队列:")
    print("   - 方法1: 拖放AE文件到 '拖放添加项目.py'")
    print("   - 方法2: 在图形界面中点击 '添加AE项目'")
    print("   - 方法3: 使用命令行: python ae_render_cli.py add 项目.aep")
    
    print("\n3. 启动渲染:")
    print("   - 图形界面: 点击 '启动渲染管理器'")
    print("   - 命令行: python ae_render_cli.py start")
    print("   - 直接启动: python ae_render_manager.py")
    
    print("\n4. 监控进度:")
    print("   - 图形界面会实时显示状态")
    print("   - 命令行: python ae_render_cli.py status")
    print("   - 查看详情: python ae_render_cli.py queue/running/completed")
    
    print("\n5. 配置系统:")
    print("   - 图形界面: 修改配置区域的参数")
    print("   - 命令行: python ae_render_cli.py config")
    print("   - 主要配置: AE路径、最大线程数")
    
    print("\n6. 文件结构:")
    print("   - ae_render_manager.py: 核心管理器")
    print("   - ae_render_gui.py: 图形界面")
    print("   - ae_render_cli.py: 命令行工具")
    print("   - 拖放添加项目.py: 拖放工具")
    print("   - config.json: 配置文件")
    print("   - queue/: 队列目录")
    print("   - running/: 运行状态目录")
    print("   - completed/: 完成记录目录")
    print("   - logs/: 日志目录")
    
    print("\n7. 注意事项:")
    print("   - 确保AE项目文件路径正确")
    print("   - 建议使用序列输出而非单个视频文件")
    print("   - 线程数不要超过CPU核心数太多")
    print("   - 定期清理日志和完成记录")
    
    print("\n8. 故障排除:")
    print("   - 检查AE路径是否正确")
    print("   - 查看logs目录中的错误日志")
    print("   - 确认项目文件没有损坏")
    print("   - 重启渲染管理器")
    
    input("\n按回车键返回...")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"程序启动失败: {e}")
        input("按回车键退出...")
